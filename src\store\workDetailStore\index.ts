import { makeAutoObservable, runInAction } from 'mobx'
import {
  getWorkDetail,
  getWorkDetailComment,
  refreshWorkDetail,
  refreshWorkComment
} from '@/services/workDetail'
import type { WorkDetail, CommentInfo } from './type'
import { message } from '@/components/GlobalTips'

class WorkDetailStore {
  workParams: {
    workId: string
    atUniqueId?: string
  } | null = null

  loading = false

  workDetail: WorkDetail | null = null

  commentInfo: CommentInfo = {
    pageNo: 1,
    pageSize: 20,
    data: [],
    hasNextPage: true,
    totalCount: 0
  }

  activeTab = 'basic'

  error: string | null = null

  // 视频播放相关状态
  currentVideoTime = 0 // 当前视频播放时间（秒）
  videoDuration = 0 // 视频总时长（秒）
  isVideoPlaying = false // 视频是否正在播放

  // 视频跳转方法，由 LeftCard 组件设置
  seekToTime: ((time: number) => void) | null = null

  constructor() {
    makeAutoObservable(this)
  }

  setWrorkParams(params: { workId: string; atUniqueId?: string }) {
    this.workParams = params
  }

  // 设置加载状态
  setLoading(loading: boolean) {
    this.loading = loading
  }

  // 设置错误信息
  setError(error: string | null) {
    this.error = error
  }

  // 设置当前激活的标签页
  setActiveTab(tab: string) {
    this.activeTab = tab
  }

  // 设置视频播放时间
  setCurrentVideoTime(time: number) {
    this.currentVideoTime = time
  }

  // 设置视频总时长
  setVideoDuration(duration: number) {
    this.videoDuration = duration
  }

  // 设置视频播放状态
  setVideoPlaying(playing: boolean) {
    this.isVideoPlaying = playing
  }

  // 跳转到指定视频时间
  jumpToVideoTime(time: number) {
    if (this.seekToTime) {
      this.seekToTime(time)
    }
  }

  // 设置作品详情
  setWorkDetail(detail: WorkDetail | null) {
    this.workDetail = detail
  }

  // 设置评论信息
  setCommentInfo(commentInfo: CommentInfo) {
    this.commentInfo = commentInfo
  }

  // 追加评论数据（用于分页加载）
  appendComments(newComments: CommentInfo) {
    this.commentInfo = {
      ...newComments,
      data: [...this.commentInfo.data, ...newComments.data]
    }
  }

  // 首次进入获取作品详情
  async fetchWorkDetail() {
    try {
      this.setLoading(true)
      this.setError(null)

      const [detailRes] = await Promise.all([getWorkDetail({ ...this.workParams! })])

      runInAction(() => {
        if (detailRes.code === 'success') {
          this.setWorkDetail(detailRes.result)
        }
        runInAction(() => {
          this.setLoading(false)
        })
      })
    } catch (error) {
      runInAction(() => {
        this.setError(error instanceof Error ? error.message : '获取作品详情失败')
      })
    }
  }

  // 获取详情
  async fetchWorkInfo() {
    try {
      const detailRes = await getWorkDetail({ ...this.workParams! })
      runInAction(() => {
        if (detailRes.code === 'success') {
          this.setWorkDetail(detailRes.result)
        }
      })
    } catch (error) {
      runInAction(() => {
        this.setError(error instanceof Error ? error.message : '获取作品详情失败')
      })
    }
  }

  // 加载更多评论
  async loadMoreComments(pageNo: number) {
    try {
      const res = await getWorkDetailComment({ ...this.workParams!, pageNo, pageSize: 20 })

      if (res.code === 'success') {
        runInAction(() => {
          this.appendComments(res.result)
        })
      }
      return res
    } catch (error) {
      runInAction(() => {
        this.setError(error instanceof Error ? error.message : '加载评论失败')
      })
      throw error
    }
  }

  // 刷新作品
  async refreshWorkInfo() {
    try {
      const res = await refreshWorkDetail({ ...this.workParams! })
      if (res.code === 'success') {
        // 刷新成功后重新获取数据
        await this.fetchWorkInfo()
        message.success('更新成功')
      }
      return res
    } catch (error) {
      runInAction(() => {
        this.setError(error instanceof Error ? error.message : '刷新作品详情失败')
      })
      throw error
    }
  }

  // 刷新评论
  async refreshComments() {
    try {
      const res = await refreshWorkComment({ ...this.workParams! })
      return res
    } catch (error) {
      runInAction(() => {
        this.setError(error instanceof Error ? error.message : '刷新评论失败')
      })
      throw error
    }
  }

  // 重置store状态
  reset() {
    this.loading = false
    this.workDetail = null
    this.workParams = null
    this.commentInfo = {
      pageNo: 1,
      pageSize: 20,
      data: [],
      hasNextPage: true,
      totalCount: 0
    }
    this.activeTab = 'basic'
    this.error = null
    // 重置视频相关状态
    this.currentVideoTime = 0
    this.videoDuration = 0
    this.isVideoPlaying = false
    this.seekToTime = null
  }
}

const workDetailStore = new WorkDetailStore()

export { WorkDetailStore }
export default workDetailStore
