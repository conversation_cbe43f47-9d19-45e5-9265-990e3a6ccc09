import React from 'react'
import { RedBookIcon } from '@/components/IconComponents'
import { TikTokFilled } from '@ant-design/icons'

export enum DateType {
  Today = '0',
  YesterDays = '1',
  SevenDays = '7',
  FifteenDays = '15',
  ThirtyDays = '30'
}

export const RankColor: Record<string, any> = {
  '0': '#ff4070',
  '1': '#00b42a',
  '2': '#165dff'
}

export const cloudColor = ['#845fe6', '#6381ed', '#8acaf3', '#f0d880', '#d97f76']

export const PlatFormObj: Record<string, any> = {
  all: {
    label: '全部',
    icon: null
  },
  // douyin: {
  //   label: '抖音',
  //   icon: React.createElement(TikTokFilled, { style: { color: '#161722' } })
  // },
  tiktok: {
    label: 'TikTok',
    icon: React.createElement(TikTokFilled, { style: { color: '#161722' } })
  }
}

const PlatFormOptions = Object.keys(PlatFormObj).map(key => ({
  label: PlatFormObj[key].label,
  value: key,
  icon: PlatFormObj[key].icon
}))

export { PlatFormOptions }
