.aiExtractionContent {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 12px 24px;
  height: 100%;
  overflow-y: auto;
}

.section {
  position: relative;
  height: 50%;
  overflow-y: auto;
  &&:first-child {
    height: 100%;
  }
  // &&:last-child {
  //   height: 55%;
  // }
  .sectionHeader {
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    background: #fff;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
  }
  .sectionTitle {
    margin: 0 !important;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

// 提取卡片
.extractionCard {
  display: flex;
  align-items: flex-start;
  padding: 12px;

  .extractionIcon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #e6f4ff;
    border-radius: 8px;
    flex-shrink: 0;
  }

  .extractionContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .extractionTitle {
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 4px;
  }

  .extractionDesc {
    font-size: 14px;
    color: #595959;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '•';
      position: absolute;
      left: 0;
      color: #1677ff;
      font-weight: bold;
    }
  }

  .extractionButton {
    align-self: flex-start;
    margin-top: 8px;
    border-radius: 6px;
    font-weight: 500;
  }
}

.quotesText {
  border-radius: 8px;
  padding: 16px 24px;
  background: #f8f9fb;
}

.quotesTimeText {
  font-size: 16px;
  font-weight: 600;
  color: #666;
  transition: color 0.2s ease;

  .highlighted & {
    color: #1677ff;
  }
}

// 台词时间段样式
.quotesTimeSegment {
  padding: 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  scroll-margin-top: 20px; // 滚动时留出顶部边距

  &:hover {
    background-color: #f5f5f5;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &.highlighted {
    background-color: #e6f4ff;
    box-shadow: 0 4px 12px rgba(22, 119, 255, 0.15);
    transform: translateY(-2px);
  }
}

// 滚动容器样式
.quotesScrollContainer {
  height: calc(100% - 41px);
  overflow-y: auto;
  scroll-behavior: smooth;
}

// 自定义 Markdown 内容样式
.markdownContent {
  padding: 0 !important;
  margin: 0 !important;
  max-width: none !important;
  min-width: auto !important;
}
