// 账号总览内容
.overviewContent {
  padding: 12px;
  padding-top: 0;

  .borderTitle {
    position: relative;
    margin-bottom: 0;
    font-size: 16px;
    &::before {
      position: absolute;
      left: -12px;
      top: 0;
      content: '';
      width: 4px;
      height: 100%;
      background: #1677ff;
      border-radius: 2px;
    }
  }

  .monitorSection {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .chartSection {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
  }

  .sectionTitle {
    display: flex;
    align-items: center;
  }

  .updateInfo {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #999;
  }

  .statsGrid {
    display: flex;
    justify-content: space-between;
    gap: 24px;
  }

  .statItem {
    flex: 1;
    text-align: left;
    padding: 12px;
    background: transparent;
    border: 1px solid #f0f0f0;

    .statLabel {
      font-size: 13px;
      color: #666;
      margin-bottom: 8px;
      font-weight: normal;
    }

    .statValue {
      font-size: 28px;
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
      line-height: 1;
    }

    .statSubValue {
      font-size: 12px;
      margin-left: 12px;
      color: #999;
    }
  }

  .chartControls {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .trendInfo {
    margin: 16px 0;
    padding: 12px 16px;
    background: #f0f7ff;
    border-radius: 6px;
    font-size: 13px;
    span {
      color: #1677ff;
    }
  }

  .chartContainer {
    width: 100%;
    height: 300px;
  }

  // 账号特征区域
  .characterSection {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .characterContent {
    display: flex;
    gap: 24px;
  }

  // 发布频率
  .publishFrequency {
    width: 50%;
    flex: 1;
  }

  .frequencyHeader {
    margin-bottom: 16px;
  }

  .frequencyTitle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .frequencyChart {
    width: 100%;
    height: 350px;
  }

  // TOP10作品标签
  .topWorks {
    width: 50%;
    flex: 1;
  }

  .topWorksHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .topWorksList {
    width: 100%;
  }

  .topWorksListHeader {
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
  }

  .tagColumn {
    width: 200px;
  }

  .percentageColumn {
    flex: 1;
    text-align: center;
  }

  .countColumn {
    width: 80px;
    text-align: right;
  }
  .topWorksItemList {
    height: 350px;
    padding-right: 12px;
    overflow: auto;
  }

  .topWorksItem {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f8f8;

    &:last-child {
      border-bottom: none;
    }
  }

  .rankNumber {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #f0f0f0;
    color: #666;
    font-size: 12px;
    margin-right: 8px;
  }

  .tagName {
    font-size: 13px;
    margin-bottom: 0;
    color: #333;
  }

  .percentageInfo {
    flex: 2;
    padding: 0 16px;
  }

  .progressBar {
    width: 100%;
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
  }

  .progressFill {
    height: 100%;
    background: #1677ff;
    border-radius: 3px;
  }

  .countInfo {
    width: 80px;
    text-align: right;
    font-size: 13px;
    color: #333;
    font-weight: 500;
  }
}
