import { WorkDetailStore as WorkDetailStoreModel } from './index'

export as namespace IWorkDetailStore

export type WorkDetailStore = WorkDetailStoreModel

// 作品基本信息
export interface WorkDetail {
  workInfo: {
    title: string
    workId: string
    thumbnailLink: string
    url: string
    isAd: number
    videoUrl: string
    playCount: number
    likeCount: number
    commentCount: number
    shareCount: number
    collectCount: number
    hashtags: string
    publishTime: number
    updateTime: number
  }
  authorInfo: {
    authorAvatar: string
    authorName: string
    workCount: number
    likeCount: number
    followerCount: number
    homeUrl: string
  }
}

// 评论信息
export interface Comment {
  commentId: 'string'
  commentLanguage: 'string'
  commenterAvatar: 'string'
  commenterDesc: 'string'
  commenterId: 'string'
  commenterName: 'string'
  commenterRegion: 'string'
  commenterSecUid: 'string'
  commenterUniqueId: 'string'
  commenterUrl: 'string'
  content: 'string'
  id: 0
  likeCount: 0
  publishTime: 0
  replayCommentTotal: 0
  workId: 'string'
}

// 评论列表信息
export interface CommentInfo {
  pageNo: number
  pageSize: number
  data: Comment[]
  hasNextPage: boolean
  totalCount: number
}

// Store状态
export interface WorkDetailState {
  loading: boolean
  workDetail: WorkDetail | null
  commentInfo: CommentInfo
  activeTab: string
  error: string | null
}
