// 左侧作品卡片
.workCard {
  width: 400px;
  height: 100%;
  flex-shrink: 0;
}

.videoCard {
  border-radius: 12px;
  overflow: hidden;
}

// 顶部账号信息
.accountHeader {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.accountInfo {
  flex: 1;
  min-width: 0;
}

.accountName {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 8px;
}

.accountStats {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.statItem {
  font-size: 13px;
  color: #8c8c8c;
  white-space: nowrap;

  .statValue {
    color: #262626;
    font-weight: 500;
    margin-left: 2px;
  }
}

// 中间视频区域
.videoContainer {
  position: relative;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.video {
  border-radius: 8px;
  background: #000;
  object-fit: cover;
  cursor: pointer;
  width: 100%;
}

.playButton {
  position: absolute;
  left: 50%;
  top: 50%;
  font-size: 48px;
  color: #fff;
  transform: translate(-50%, -50%);
  opacity: 0.9;
  cursor: pointer;
  z-index: 2;

  &:hover {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
    transition: all 0.2s ease;
  }
}

.videoDuration {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

// 底部下载区域
.downloadSection {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.downloadButton {
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
  background-color: #f8f9fb;
}
