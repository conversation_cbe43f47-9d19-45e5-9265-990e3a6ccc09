{"editor.formatOnType": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "css.lint.unknownAtRules": "ignore", "scss.lint.unknownAtRules": "ignore", "less.compile": {"compress": false, "sourceMap": false, "out": false}, "easysass.compileOnSave": false, "liveSassCompile.settings.autoprefix": [], "liveSassCompile.settings.generateMap": false, "compile-hero.disable-compile-files-on-did-save-code": true}