import { CORE } from '@/constants/service'
import http, { ResponseResult } from '@/utils/http'
import { v4 as uuidv4 } from 'uuid'

const getHotSaleSetting = () => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 'success',
        msg: '',
        result: {
          playCount: '10000',
          likeCount: '5000',
          commentCount: '2000',
          shareCount: '1000',
          collectCount: '1000',
          fansIncrease: '500'
        }
      })
    }, 500)
  })
}

const setHotSaleSetting = (params: any) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 'success',
        msg: '',
        result: ''
      })
    }, 500)
  })
}

const getHotSaleList = (
  data: {
    startTime?: string | number
    endTime?: string | number
    platform?: string
  } & Global.PageParams
): Promise<ResponseResult<any>> => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 'success',
        msg: '',
        result: {
          pageNo: data.pageNo,
          pageSize: data.pageSize,
          totalCount: 0,
          data: [
            {
              workId: '123'
            },
            {
              workId: '124'
            },
            {
              workId: '125'
            }
          ]
        }
      })
    }, 500)
  })
  // return http.request({
  //   url: '/author/dashboard/authorWorkList',
  //   method: 'post',
  //   data: { ...data, atUniqueId: 'nailtitude_official' }
  // })
}

export { getHotSaleSetting, setHotSaleSetting, getHotSaleList }
