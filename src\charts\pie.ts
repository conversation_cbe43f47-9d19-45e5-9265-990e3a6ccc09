import { RankColor } from '@/constants'
import { type EChartsOption } from 'echarts'

const defaultOptions: EChartsOption = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    top: '25%',
    right: '0',
    orient: 'vertical'
  },
  series: {
    type: 'pie',
    center: ['40%', '50%'],
    radius: ['50%', '90%'],
    top: 40,
    bottom: 0,
    left: 10,
    avoidLabelOverlap: false,
    itemStyle: {
      borderColor: '#fff',
      borderWidth: 2
    },
    label: {
      show: false,
      position: 'center'
    },
    emphasis: {
      label: {
        show: true,
        fontSize: 20,
        fontWeight: 'bold'
      }
    },
    labelLine: {
      show: false
    },
    data: []
  }
}
