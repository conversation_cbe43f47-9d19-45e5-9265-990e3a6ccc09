import { globalStore } from '@/store'
import type { EChartsOption, WordCloudSeriesOption } from 'echarts'
const colorList = ['#845fe6', '#6381ed', '#8acaf3', '#f0d880', '#d97f76']
const defaultCloudOption = {
  type: 'wordCloud',
  shape: 'circle',
  sizeRange: [14, 24],
  rotationRange: [0, 0],
  rotationStep: 45,
  drawOutOfBound: false,
  layoutAnimation: true,
  left: 'center',
  bottom: 0,
  keepAspect: true,
  shrinkToFit: true,
  maskImage: globalStore.maskCloudImage,
  textStyle: {
    fontFamily: 'sans-serif',
    fontWeight: 'bold',
    color: function (item) {
      return colorList[item.dataIndex % 5]
    }
  }
} as WordCloudSeriesOption
