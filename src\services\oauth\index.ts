export * from './douyin'
export * from './redbook'
export * from './repast'

import { CORE } from '@/constants/service'
import http, { ResponseResult } from '@/utils/http'

/**
 * 通用账号搜索接口
 * 根据平台类型搜索账号
 */
export const searchAccountsByPlatform = (data: {
  platform: string
  search?: string
  pageNo?: number
  pageSize?: number
}): Promise<ResponseResult> => {
  const { platform, search, pageNo = 1, pageSize = 10 } = data

  // 根据平台类型调用不同的接口
  if (platform === 'douyin') {
    return http.request({
      url: `${CORE}/oauth/simple/list`,
      method: 'post',
      data: {
        authType: 'DOUYIN',
        search,
        pageNo,
        pageSize
      }
    })
  } else if (platform === 'tiktok') {
    // TikTok 使用相同的接口，但 authType 不同
    return http.request({
      url: `${CORE}/oauth/simple/list`,
      method: 'post',
      data: {
        authType: 'TIKTOK',
        search,
        pageNo,
        pageSize
      }
    })
  }

  // 如果平台不支持，返回空结果
  return Promise.resolve({
    code: 'success',
    msg: '',
    result: {
      list: [],
      total: 0
    }
  })
}
