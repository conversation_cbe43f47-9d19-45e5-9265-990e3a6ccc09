import { authStore } from '@/store'
import { LockOutlined, MailOutlined, UserOutlined } from '@ant-design/icons'
import { CaptFieldRef, LoginForm, ProFormCaptcha, ProFormText } from '@ant-design/pro-components'
import { Button, Divider, Typography } from 'antd'
import { useRef } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { getMessageCode } from '@/services/auth'
import styles from './index.module.less'
import { message } from '@/components/GlobalTips'

const { Title } = Typography
const webUrl = import.meta.env.VITE_WEB_URL

const LoginFormSection = () => {
  return (
    <div>
      <ProFormText
        name="email"
        fieldProps={{
          size: 'large',
          prefix: <MailOutlined className={'prefixIcon'} />
        }}
        placeholder="邮箱"
        rules={[
          {
            required: true,
            message: '请输入邮箱！'
          },
          {
            pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
            message: '邮箱格式错误！'
          }
        ]}
      />
      <ProFormText.Password
        name="password"
        fieldProps={{
          size: 'large',
          prefix: <LockOutlined className={'prefixIcon'} />
        }}
        placeholder={'请输入密码'}
        rules={[
          {
            required: true,
            message: '请输入密码！'
          }
        ]}
      />
    </div>
  )
}

const RegisterFormSection = () => {
  const captchaRef = useRef<CaptFieldRef | null | undefined>()

  return (
    <div>
      <ProFormText
        label="用户名"
        tooltip="用户长度为2-20个字符"
        name="username"
        fieldProps={{
          size: 'large',
          prefix: <UserOutlined />
        }}
        placeholder="邮箱"
        rules={[
          {
            required: true,
            message: '请输入用户名！'
          },
          {
            min: 2,
            max: 20,
            message: '用户名长度为2-20个字符！'
          }
        ]}
      />
      <ProFormText
        label="邮箱"
        name="email"
        fieldProps={{
          size: 'large',
          prefix: <MailOutlined />
        }}
        placeholder="邮箱"
        rules={[
          {
            required: true,
            message: '请输入邮箱！'
          },
          {
            pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
            message: '邮箱格式错误！'
          }
        ]}
      />
      <ProFormCaptcha
        fieldRef={captchaRef}
        fieldProps={{
          size: 'large'
        }}
        captchaProps={{
          size: 'large'
        }}
        phoneName="email"
        placeholder={'请输入验证码'}
        captchaTextRender={(timing, count) => {
          if (timing) {
            return `${count} ${'获取验证码'}`
          }
          return '获取验证码'
        }}
        name="messageCode"
        rules={[
          {
            required: true,
            message: '请输入验证码！'
          }
        ]}
        onGetCaptcha={async value => {
          await getMessageCode({ email: value, type: 2 }).then(res => {
            if (res.code === 'success') {
              message.success(res.msg)
            } else {
              message.error(res.msg)
              throw new Error()
            }
          })
        }}
      />
      <ProFormText.Password
        label="密码"
        tooltip="密码长度为6-20个字符"
        name="password"
        fieldProps={{
          size: 'large',
          prefix: <LockOutlined />
        }}
        placeholder={'请输入密码'}
        rules={[
          {
            required: true,
            message: '请输入密码！'
          }
        ]}
      />
    </div>
  )
}

const Login: React.FC<{ loginState: 'login' | 'register' }> = ({ loginState }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const searchParams = new URLSearchParams(location.search)
  const rednerBottom = () => {
    return loginState === 'register' ? (
      <div>
        已有账号？
        <Button type="link" onClick={() => navigate('/login')}>
          立即登录
        </Button>
      </div>
    ) : (
      <div>
        还没有账号？
        <Button type="link" onClick={() => navigate('/register')}>
          立即注册
        </Button>
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className={styles.leftSection}></div>
      <div className={styles.rightSection}>
        <div className={styles.loginContainer}>
          <LoginForm
            key={loginState}
            contentStyle={{
              minWidth: 350
            }}
            onFinish={async (val: IAuthStore.LoginParams | IAuthStore.RegisterParams) => {
              if (loginState === 'login') {
                const params: IAuthStore.LoginParams = {
                  ...val,
                  type: 1,
                  loginType: 2
                }
                await authStore.login(params).then(() => {
                  navigate(searchParams.get('redirect') ?? '/')
                  message.success('登录成功')
                })
              }
              if (loginState === 'register') {
                const params: IAuthStore.RegisterParams = {
                  ...val,
                  loginType: 2,
                  type: 2
                }
                await authStore.register(params).then(() => {
                  navigate('/login')
                  message.success('注册成功')
                })
              }
            }}
            actions={
              <div style={{ textAlign: 'center' }}>
                {/* 登录即代表同意
                <Button
                  target="_blank"
                  type="link"
                  href={`${webUrl}/serviceAgreement`}
                  style={{ padding: 0 }}
                >
                  服务协议
                </Button>
                和
                <Button
                  target="_blank"
                  type="link"
                  href={`${webUrl}/privacyAgreement`}
                  style={{ padding: 0 }}
                >
                  隐私协议
                </Button> */}
                <Divider style={{ margin: 6 }} />
                {rednerBottom()}
              </div>
            }
            submitter={{
              searchConfig: {
                submitText: loginState === 'login' ? '登录' : '注册'
              }
            }}
          >
            <Title level={3} style={{ textAlign: 'center' }}>
              {loginState === 'login' ? '登录' : '注册'}
            </Title>
            {loginState === 'login' ? <LoginFormSection /> : <RegisterFormSection />}
          </LoginForm>
        </div>
      </div>
    </div>
  )
}

export default Login
