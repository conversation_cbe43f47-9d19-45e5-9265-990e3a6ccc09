import { useRef } from 'react'
import { isEmpty } from 'lodash-es'
import { message } from '@/components/GlobalTips'

import { getOauthUrl, addOauthUserToken } from '@/services/oauth'

const useConnectorOauth = () => {
  const oauthWindowRef = useRef<any>(null)

  // 发起授权 返回`accessToken`
  const launchOauth = async (parameters: any) => {
    const { code, result, msg: urlMsg } = await getOauthUrl(parameters)
    if (code !== 'success') {
      message.error(urlMsg)
      return
    }

    if (!oauthWindowRef.current || oauthWindowRef.current.closed) {
      const screenX = window.outerWidth / 2 + window.screenX - 400
      const screenY = window.outerHeight / 2 + window.screenY - 300

      oauthWindowRef.current = window.open(
        result,
        '_blank',
        `width=800px,height=600px,screenX=${screenX},screenY=${screenY},menubar=no,status=no,toolbar=no`
      )
    } else {
      oauthWindowRef.current.location.href = result
      oauthWindowRef.current.focus()
    }

    const oauthFields = await new Promise<Record<string, string>>(resolve => {
      window.onmessage = evt => {
        const { event, value } = evt.data
        if (event === 'oauth-code') {
          const fields: { [key: string]: any } = {}
          if (!Reflect.has(fields, 'code') && value?.code) {
            fields!.code = value?.code
          }

          if (isEmpty(fields)) {
            message.error('授权失败')
          } else {
            resolve(fields)
          }
        }
      }
    })
    const {
      code: tokenCode,
      result: tokenResult,
      msg
    } = await addOauthUserToken({
      ...oauthFields,
      ...parameters
    })
    if (tokenCode !== 'success') {
      message.error(msg)
      return Promise.reject()
    }
    return tokenResult
  }

  return { oauthWindowRef, launchOauth }
}

export default useConnectorOauth
