import React from 'react'
import styles from './index.module.less'
import classNames from 'classnames'

type ContentSectionProps = {
  noMargin?: boolean
  style?: React.CSSProperties
  className?: string
  children?: any
}

const ContentSection: React.FC<ContentSectionProps> = ({
  children,
  style,
  className,
  noMargin
}) => {
  return (
    <div
      className={classNames(styles.contentModule, { [styles.noMargin]: noMargin }, className)}
      style={style}
    >
      {children}
    </div>
  )
}

export default ContentSection
