import http, { ResponseResult } from '@/utils/http'

const getAccountTraceList = (data: {
  pageNo: number
  pageSize: number
  platform?: string
  search?: string
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/monitor/author/list',
    method: 'post',
    data
  })
}

const deleteAccountTrace = (params: { atUniqueId: string }): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/monitor/author/remove',
    method: 'get',
    params
  })
}

const getAccountInfo = ({
  search,
  platform
}: {
  search: string
  platform: string
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/monitor/author/search',
    method: 'get',
    params: {
      homeUrl: search
    }
  })
}

const addAccountTrace = (params: { atUniqueId: string }): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/monitor/author/add',
    method: 'get',
    params
  })
}

export { getAccountTraceList, getAccountInfo, addAccountTrace, deleteAccountTrace }
