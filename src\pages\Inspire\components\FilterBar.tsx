import React, { useState, useEffect } from 'react'
import { Tag, Space, DatePicker, Input, Button, message } from 'antd'
import { TikTokFilled, SearchOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import CollectionModal from './CollectionModal'

const platformOptions = [
  { label: '全部', value: 'all' },
  { label: 'TikTok', value: 'tiktok', icon: <TikTokFilled style={{ color: '#000000' }} /> }
  // { label: '抖音', value: 'douyin', icon: <TikTokFilled style={{ color: '#000000' }} /> }
]

const timeOptions = [
  { label: '全部', value: 'all' },
  { label: '近7日', value: '7d' },
  { label: '近14日', value: '14d' },
  { label: '近30日', value: '30d' },
  { label: '自定义', value: 'custom' }
]

const workTypeOptions = [
  { label: '全部', value: 'all' },
  { label: '广告素材', value: 'ad_material' },
  { label: '自然流素材', value: 'organic_material' }
]

const tagOptions = [
  { label: '全部', value: 'all' },
  { label: '复用脚本结构', value: 'script_reuse' },
  { label: '拍摄观角素材', value: 'shooting_angle' },
  { label: '短快热门爆款', value: 'trending_hot' }
]

const { RangePicker } = DatePicker

export interface FilterBarValue {
  platform: string
  workType: string
  tag: string
  time: string
  customRange: any
  searchKeyword: string
}

interface FilterBarProps {
  value: FilterBarValue
  onChange: (val: FilterBarValue) => void
}

const FilterBar: React.FC<FilterBarProps> = ({ value, onChange }) => {
  const [platform, setPlatform] = useState(value.platform)
  const [workType, setWorkType] = useState(value.workType)
  const [tag, setTag] = useState(value.tag)
  const [time, setTime] = useState(value.time)
  const [customRange, setCustomRange] = useState<any>(value.customRange)
  const [searchKeyword, setSearchKeyword] = useState(value.searchKeyword)
  const [showRange, setShowRange] = useState(false)
  const [collectionModalOpen, setCollectionModalOpen] = useState(false)

  // 只有当自定义日期范围选择完成时，其他Tag才不高亮
  const isCustomSelected = !!customRange && customRange[0] && customRange[1]

  // 处理RangePicker变化
  const handleRangeChange = (val: any) => {
    setCustomRange(val)
    if (!val || !val[0] || !val[1]) {
      // 清空日期选择时，回到"全部"状态
      setShowRange(false)
      setTime('all')
    } else {
      // 选择了具体日期时，设置为自定义状态并触发接口调用
      setShowRange(true)
      setTime('custom')
    }
  }

  // 处理自定义按钮点击
  const handleCustomClick = () => {
    setShowRange(true)
    // 不改变 time 状态，保持之前的筛选条件
  }

  // 处理其他时间选项点击
  const handleTimeOptionClick = (optionValue: string) => {
    setTime(optionValue)
    setCustomRange(null)
    setShowRange(false)
  }

  // 获取日期选择的禁用日期函数
  const disabledDate = (current: any) => {
    // 禁用今天之后的日期和一个月之前的日期
    const today = dayjs()
    const oneMonthAgo = today.subtract(1, 'month')
    return current && (current > today || current < oneMonthAgo)
  }

  // 处理采集确认
  const handleCollectionConfirm = (data: { platform: string; url: string }) => {
    console.log('采集数据:', data)
    message.success('采集任务已提交，正在处理中...')
    setCollectionModalOpen(false)
  }

  const handleCollectionOk = () => {
    setCollectionModalOpen(false)
    onChange({ platform, workType, tag, time, customRange, searchKeyword })
  }

  // 联动外部
  useEffect(() => {
    onChange({ platform, workType, tag, time, customRange, searchKeyword })
  }, [platform, workType, tag, time, customRange, searchKeyword])

  return (
    <div style={{ background: '#fff', padding: 16, borderRadius: 8 }}>
      {/* 平台筛选 */}
      <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
        <div style={{ marginRight: 8, fontWeight: 500, width: 80 }}>平台：</div>
        <Space size={12} wrap style={{ flex: 1 }}>
          {platformOptions.map(opt => (
            <Tag.CheckableTag
              key={opt.value}
              checked={platform === opt.value}
              onChange={() => setPlatform(opt.value)}
              className="selectTag"
            >
              <Space size={12}>
                {opt.icon && opt.icon}
                {opt.label}
              </Space>
            </Tag.CheckableTag>
          ))}
        </Space>
      </div>

      {/* 标签筛选 */}
      {/* <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
        <span style={{ marginRight: 8, fontWeight: 500, width: 80 }}>标签：</span>
        <Space size={4} wrap>
          {tagOptions.map(opt => (
            <Tag.CheckableTag
              key={opt.value}
              checked={tag === opt.value}
              onChange={() => setTag(opt.value)}
              style={{ padding: '3px 12px', fontSize: 15 }}
            >
              {opt.label}
            </Tag.CheckableTag>
          ))}
        </Space>
      </div> */}

      {/* 作品类型筛选 */}
      <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
        <div style={{ marginRight: 8, fontWeight: 500, width: 80 }}>作品类型：</div>
        <Space size={12} wrap style={{ flex: 1 }}>
          {workTypeOptions.map(opt => (
            <Tag.CheckableTag
              key={opt.value}
              checked={workType === opt.value}
              onChange={() => setWorkType(opt.value)}
              className="selectTag"
            >
              {opt.label}
            </Tag.CheckableTag>
          ))}
        </Space>
      </div>

      {/* 时间筛选 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ marginRight: 8, fontWeight: 500, width: 80 }}>保存时间：</div>
        <Space size={12} wrap style={{ flex: 1 }}>
          {timeOptions.map(opt => {
            if (opt.value === 'custom') {
              // 只替换自定义按钮为RangePicker
              return showRange ? (
                <RangePicker
                  key="rangepicker"
                  allowClear
                  style={{ marginLeft: 8, height: 26 }}
                  value={customRange}
                  onChange={handleRangeChange}
                  disabledDate={disabledDate}
                  placeholder={['开始日期', '结束日期']}
                />
              ) : (
                <Tag.CheckableTag
                  key={opt.value}
                  checked={false}
                  onChange={handleCustomClick}
                  className="selectTag"
                >
                  {opt.label}
                </Tag.CheckableTag>
              )
            }
            // 只有当自定义日期范围选择完成时，其他Tag才不高亮
            return (
              <Tag.CheckableTag
                key={opt.value}
                checked={!isCustomSelected && time === opt.value}
                onChange={() => handleTimeOptionClick(opt.value)}
                className="selectTag"
              >
                {opt.label}
              </Tag.CheckableTag>
            )
          })}
        </Space>
      </div>

      {/* 搜索栏和操作按钮 */}
      <div
        style={{
          marginTop: 16,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}
      >
        {/* 左侧搜索栏 */}
        <div style={{ flex: 1, maxWidth: 400 }}>
          <Input
            placeholder="搜索作品标题、标签等..."
            prefix={<SearchOutlined />}
            value={searchKeyword}
            onChange={e => setSearchKeyword(e.target.value)}
            allowClear
            size="middle"
          />
        </div>

        {/* 右侧操作按钮 */}
        <div style={{ display: 'flex', gap: 12 }}>
          <Button type="primary" size="middle" onClick={() => setCollectionModalOpen(true)}>
            作品采集
          </Button>
          <Button size="middle">批量操作</Button>
        </div>
      </div>

      {/* 采集弹窗 */}
      {collectionModalOpen && (
        <CollectionModal
          open={collectionModalOpen}
          onCancel={() => setCollectionModalOpen(false)}
          onOk={() => handleCollectionOk()}
        />
      )}
    </div>
  )
}

export default FilterBar
