.markdown-body {
  box-sizing: border-box;
  min-width: 200px;
  max-width: 980px;
  margin: 0 auto;
  padding: 15px;

  pre {
    background-color: #1e1e1e;
    border-radius: 6px;
    padding: 16px;
    overflow: auto;
  }

  code {
    background-color: rgba(175, 184, 193, 0.2);
    border-radius: 6px;
    padding: 0.2em 0.4em;
    font-size: 85%;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  table {
    border-spacing: 0;
    border-collapse: collapse;
    display: block;
    width: max-content;
    max-width: 100%;
    overflow: auto;

    th,
    td {
      padding: 6px 13px;
      border: 1px solid #d0d7de;
    }

    tr:nth-child(2n) {
      background-color: #f6f8fa;
    }
  }

  blockquote {
    margin: 0;
    padding: 0 1em;
    color: #656d76;
    border-left: 0.25em solid #d0d7de;
  }
}
