interface IStorageValue<T = any> {
  value: T
  time: number
  expire: number
}

interface IStorageConfig {
  name: string
  isSession?: boolean
  expire?: number
}

const defaultConfig: IStorageConfig = {
  name: 'storage_key',
  isSession: false,
  expire: 0
}

export class NewStorage {
  storage: Storage

  _key: string

  expire!: number

  callback?: (data: any) => void

  constructor(config: IStorageConfig = defaultConfig) {
    const props = {
      ...defaultConfig,
      ...config
    }
    this.storage = props.isSession ? window.sessionStorage : window.localStorage
    this._key = props.name
  }

  get key() {
    return this._key
  }

  static createStorage(config: IStorageConfig) {
    return new NewStorage(config)
  }

  set(value: any) {
    this.storage.setItem(
      this.key,
      JSON.stringify({
        value,
        time: Date.now(),
        expire: this.expire || 0
      })
    )
    this.callback && this.callback(value)
  }

  get() {
    let storageValue: any = this.storage.getItem(this.key)
    if (!storageValue) {
      return null
    }
    try {
      storageValue = JSON.parse(storageValue) as IStorageValue
    } catch {
      return null
    }
    if (storageValue.expire > 0) {
      if (Date.now() > storageValue.time + storageValue.expire) {
        this.remove()
        return null
      }
    }
    return storageValue.value
  }

  remove() {
    this.storage.removeItem(this.key)
    this.callback && this.callback(null)
  }

  clear() {
    this.storage.clear()
  }

  storageEventHandle = (e: StorageEvent) => {
    if (e.key === this.key) {
      this.callback && this.callback(e.newValue)
    }
  }

  addEventListener(callback: (data: any) => void, force = true) {
    if (!this.callback) {
      this.callback = callback
    }
    force && window.addEventListener('storage', this.storageEventHandle)
  }

  removeEventListener() {
    this.callback = undefined
    window.removeEventListener('storage', this.storageEventHandle)
  }
}

export const authInfoStorage = NewStorage.createStorage({
  name: 'authInfo',
  expire: 0
})
