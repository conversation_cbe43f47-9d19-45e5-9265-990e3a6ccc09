declare namespace Global {
  type PageParams = {
    pageNo: number
    pageSize: number
    search?: string
  }
}

declare interface IStore {
  globalStore: IGlobalStore.GlobalStore
  authStore: IAuthStore.AuthStore
  workDetailStore: IWorkDetailStore.WorkDetailStore
  accountDetailStore: IAccountDetailStore.AccountDetailStore
}

declare namespace Server {
  type PageResult<T> = {
    data: T
    pageNo: number
    pageSize: number
    totalCount: number
  }
}
