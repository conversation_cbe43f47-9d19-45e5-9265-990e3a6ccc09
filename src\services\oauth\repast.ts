import { CORE } from '@/constants/service'
import { AESEncryptPwd } from '@/utils/aes'
import http, { ResponseResult } from '@/utils/http'

// 美团大众点评
/**
 * 业务场景: 添加账户
 */
export const addAccount = (data: {
  account: string
  password: string
  nickname: string
}): Promise<ResponseResult> => {
  data.password = AESEncryptPwd(data.password)
  return http.request({
    url: `${CORE}/account/add`,
    method: 'post',
    data
  })
}

/**
 * 业务场景: 获取账户列表
 */
export const getAccountList = (
  data: {
    search?: string
  } & Global.PageParams
): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/account/list`,
    method: 'post',
    data
  })
}

/**
 * 业务场景: 获取账户详情
 */
export const getAccountInfo = (params: { authId: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/account/info`,
    method: 'get',
    params
  })
}

/**
 * 业务场景: 删除账户
 */
export const removeAccount = (params: { authId: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/account/remove`,
    method: 'get',
    params
  })
}

/**
 * 业务场景: 更新账户
 */
export const updateAccount = (data: {
  authId?: string
  authType?: string
  account: string
  password: string
  nickname: string
}): Promise<ResponseResult> => {
  data.password = AESEncryptPwd(data.password)
  return http.request({
    url: `${CORE}/account/update`,
    method: 'post',
    data
  })
}
