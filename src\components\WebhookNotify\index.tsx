import { Tabs, Form, Input } from 'antd'
const webhookList = [
  { key: 'qw-robot', title: '企业微信', secretKey: '' },
  { key: 'dingtalk-robot', title: '钉钉', secretKey: 'dingtalk-secret' },
  { key: 'lark-robot', title: '飞书', secretKey: 'lark-secret' }
]

export const getDefaultWebhookValue = () => {
  return [
    {
      channelType: 'qw-robot',
      content: {
        url: ''
      }
    },
    {
      channelType: 'dingtalk-robot',
      content: {
        url: '',
        secret: ''
      }
    },
    {
      channelType: 'lark-robot',
      content: {
        url: '',
        secret: ''
      }
    }
  ]
}
export const WebhookNotify: React.FC<{
  id?: string
  value?: any
  onChange?: (val: any) => void
}> = props => {
  const { value, onChange } = props

  const onInputChange = (inputvalue: string, key: string, type: string) => {
    const valueObj = value.filter((_data: any) => _data.channelType === key)[0].content
    if (type === 'webhook') {
      valueObj.url = inputvalue
    }
    if (type === 'secret') {
      valueObj.secret = inputvalue
    }
    onChange!([...value])
  }
  return (
    <Tabs
      style={{ paddingLeft: '5px' }}
      tabBarStyle={{ marginTop: '-7px' }}
      items={webhookList.map(item => {
        const currentValue = value.filter((_data: any) => _data.channelType === item.key)[0].content
        return {
          key: item.key,
          label: item.title,
          children: (
            <>
              <Form.Item layout="horizontal" label="webhook地址" labelCol={{ flex: '110px' }}>
                <Input
                  onChange={_value => {
                    onInputChange(_value.target.value, item.key, 'webhook')
                  }}
                  autoComplete="off"
                  value={currentValue.url}
                  placeholder="请输入Webhook地址"
                />
              </Form.Item>
              {item.secretKey && (
                <Form.Item layout="horizontal" label="密钥" labelCol={{ flex: '110px' }}>
                  <Input
                    onChange={_value => {
                      onInputChange(_value.target.value, item.key, 'secret')
                    }}
                    value={currentValue.secret}
                    autoComplete="off"
                    placeholder="请输入密钥"
                  />
                </Form.Item>
              )}
            </>
          )
        }
      })}
    />
  )
}
