import { useState } from 'react'
import { Button } from 'antd'
import { SettingOutlined } from '@ant-design/icons'
import FilterBar, { FilterBarValue } from './components/FilterBar'
import CardList from './components/CardList'
import MonitorSettingModal from './components/MonitorSettingModal'
import FloatBackTop from '@/components/FloatBackTop'

const defaultFilter: FilterBarValue = {
  platform: 'all',
  time: 'all',
  customRange: null
}

const HotMonitor = () => {
  const [filter, setFilter] = useState<FilterBarValue>(defaultFilter)

  return (
    <>
      <div
        id="hotSale"
        style={{
          background: '#fff',
          borderRadius: 8,
          height: 'calc(100vh - 68px)',
          overflow: 'auto'
        }}
      >
        <FilterBar value={filter} onChange={setFilter} />
        <CardList filter={filter} />
        <FloatBackTop id="hotSale" />
      </div>
    </>
  )
}

const HotSale = () => {
  const [modalOpen, setModalOpen] = useState(false)

  return (
    <>
      <div className="header" style={{ padding: '0 12px 12px' }}>
        <div className="left" style={{ fontSize: 18, fontWeight: 600 }}>
          爆款监控
        </div>
        <div className="right">
          <Button type="text" icon={<SettingOutlined />} onClick={() => setModalOpen(true)}>
            监控设置
          </Button>
        </div>
      </div>
      <HotMonitor />
      <MonitorSettingModal open={modalOpen} onCancel={() => setModalOpen(false)} />
    </>
  )
}

export default HotSale
