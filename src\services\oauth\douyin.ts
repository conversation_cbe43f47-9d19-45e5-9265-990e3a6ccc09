import { CORE } from '@/constants/service'
import http, { ResponseResult } from '@/utils/http'

// 抖音app授权
/**
 * 获取抖音app三方地址
 */
export const getOauthUrl = (data: { oauthType: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/oauth/getUrl`,
    method: 'post',
    data
  })
}

/**
 * 授权
 */
export const addOauthUserToken = (data: {
  code: string
  oauthType: string
}): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/oauth/addUserToken`,
    method: 'post',
    data
  })
}

/**
 * 获取抖音app授权数据
 */
export const getOauthUserList = (data: {
  pageNo: number
  pageSize: number
  oauthType: string
}): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/oauth/list`,
    method: 'post',
    data
  })
}

/**
 * 移除抖音app授权数据
 */
export const removeOauthUser = (data: { codeList?: string[] }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/oauth/remove`,
    method: 'post',
    data
  })
}

/**
 * 修改抖音app备注
 */
export const changeRemarkOauthUser = (params: {
  uuid: string
  remark: string
}): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/oauth/update/remark`,
    method: 'get',
    params
  })
}

/**
 * 获取授权抖音账号列表-下拉框
 */
export const getOauthSimpleList = (
  data: {
    authType?: string
    search?: string
  } & Global.PageParams
): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/oauth/simple/list`,
    method: 'post',
    data: {
      ...data,
      authType: 'DOUYIN'
    }
  })
}

// 抖音直播授权
/*
 * 扫描授权信息
 */
export const getQrCodeInfo = (params: { uuid: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/douyin/auth/info`,
    method: 'get',
    params
  })
}

/*
 * 获取用户授权账号列表
 */
export const getQrCodeList = (
  data: {
    authStatus?: number
    oauthType?: string
    startTime?: string
    endTime?: string
    uuid?: string
  } & Global.PageParams
): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/douyin/list`,
    method: 'post',
    data
  })
}

/*
 * 新增授权账号
 */
export const newDouYinQrCode = (): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/douyin/qrcode/new`,
    method: 'post',
    data: {
      qrAuthType: 'qr_code_douyin'
    }
  })
}

/*
 * 重新授权
 */
export const reauthDouYinQrCode = (data: { uuid: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/douyin/qrcode/reauth`,
    method: 'post',
    data
  })
}

/*
 * 删除授权的用户
 */
export const deleteLiveAuth = (params: {
  uuid: string
  oauthType?: string
}): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/douyin/auth/delete`,
    method: 'get',
    params
  })
}

/*
 * 验证码授权
 */
export const updateSmscode = (data: { uuid: string; smsCode: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/douyin/update/smscode`,
    method: 'post',
    data
  })
}
