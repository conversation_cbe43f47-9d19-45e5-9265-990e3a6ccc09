import { CORE } from '@/constants/service'
import http, { ResponseResult } from '@/utils/http'

// 账号基础数据详情
const getAccountDetail = (params: { atUniqueId: string }): Promise<ResponseResult<any>> => {
  return http.request({
    url: `${CORE}/author/dashboard/authorInfo`,
    method: 'get',
    params
  })
}

// 刷新账号基础数据
const refreshAccountDetail = (params: { atUniqueId: string }): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/monitor/author/update',
    method: 'get',
    params
  })
}

// 增长趋势图表
const getIncreaseChart = (params: {
  atUniqueId: string
  datType?: string | number
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/author/dashboard/interactiveChart',
    method: 'get',
    params
  })
}

// 发布频率图表数据
const getPublishFrequency = (data: {
  atUniqueId: string
  dateType?: string | number
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/author/dashboard/workReleaseFrequency',
    method: 'post',
    data
  })
}

// TOP10作品标签
const getTopWorksLabel = (params: {
  atUniqueId: string
  sortType?: string | number
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/author/dashboard/workTagRanking',
    method: 'get',
    params
  })
}

// 监控达人作品列表
const getAuthorWorkList = (
  data: {
    hotVideo?: number //0: 不为爆款 1: 为爆款 不传默认
    atUniqueId: string
    search?: string // 搜索关键词
    topicTag?: string // 话题标签
    startTime?: string | number
    endTime?: string | number
    sortFields?: string | number // 排序字段 0:发布时间, 1:点赞数, 2:评论数, 3:转发数, 4:收藏数 不传为默认
    sortOrder?: string | number //排序字段 0: 倒序 1: 正序  不传为默认
  } & Global.PageParams
): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/author/dashboard/authorWorkList',
    method: 'post',
    data
  })
}

// 获取话题标签列表
const getTopicTagList = (params: { atUniqueId: string }): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/author/dashboard/topicTagList',
    method: 'get',
    params
  })
}

// 获取账号诊断结果
const getAuthorTaskResult = (params: { atUniqueId: string }): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/ai/action/queryAuthorTaskResult',
    method: 'get',
    params
  })
}

// 添加账号分析
const addAuthorDisassembly = (params: { atUniqueId: string }): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/ai/action/addAuthorDisassembly',
    method: 'get',
    params
  })
}

export {
  getAccountDetail,
  getTopWorksLabel,
  refreshAccountDetail,
  getIncreaseChart,
  getPublishFrequency,
  getAuthorWorkList,
  getTopicTagList,
  getAuthorTaskResult,
  addAuthorDisassembly
}
