// 账号信息区域
.accountInfo {
  padding: 12px 20px;
  background: #fff;
  margin: 12px 24px;
  margin-top: 0;
  border-radius: 12px;
  border: 1px solid #e8e8e8;
}

.accountHeader {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.accountMeta {
  flex: 1;
}

.accountNameRow {
  margin-bottom: 8px;
}

.accountTags {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 6px;
  flex-wrap: wrap;
}

.categoryTag,
.locationTag,
.timeTag {
  font-size: 12px;
  color: #666;
  background: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
}

.accountStats {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 12px 0;
  flex-wrap: wrap;
}

.statGroup {
  display: flex;
  align-items: center;
  gap: 4px;
}

.statLabel {
  font-size: 13px;
  color: #666;
}

.statValue {
  font-size: 13px;
  font-weight: 600;
  color: #333;
}

.statDivider {
  color: #d9d9d9;
  font-size: 12px;
}

.accountDescription {
  margin-top: 8px;
  line-height: 1.5;

  .ant-typography {
    font-size: 13px;
    color: #333;
  }
}
