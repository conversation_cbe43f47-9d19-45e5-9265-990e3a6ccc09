import { useCallback, useEffect, useRef, useState } from 'react'

interface UseInfiniteScrollOptions {
  threshold?: number
  rootMargin?: string
}

export const useInfiniteScroll = <T>(
  initialData: T[],
  loadMoreFn: (currentData: T[]) => Promise<T[]> | T[],
  hasMore: boolean,
  dependencies: React.DependencyList = [],
  options: UseInfiniteScrollOptions = {}
) => {
  const { threshold = 0.1, rootMargin = '0px' } = options
  const [data, setData] = useState<T[]>(initialData)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const loaderRef = useRef<HTMLDivElement>(null)

  const loadMore = useCallback(async () => {
    if (loading || !hasMore) {
      return
    }

    setLoading(true)
    setError(null)

    try {
      const newData = await loadMoreFn(data)
      setData(prev => [...prev, ...newData])
    } catch (err) {
      const error = err instanceof Error ? err : new Error('加载数据失败')
      setError(error)
      console.error('加载更多数据失败:', error)
    } finally {
      setLoading(false)
    }
  }, [loading, hasMore, loadMoreFn, data])

  // IntersectionObserver 监听
  useEffect(() => {
    if (!hasMore || !loaderRef.current) {
      return
    }

    const observer = new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting) {
          loadMore()
        }
      },
      { threshold, rootMargin }
    )

    const currentLoader = loaderRef.current
    observer.observe(currentLoader)

    return () => {
      if (currentLoader) {
        observer.unobserve(currentLoader)
      }
    }
  }, [hasMore, loadMore, threshold, rootMargin, ...dependencies])

  const reset = useCallback((newData: T[]) => {
    setData(newData)
    setError(null)
  }, [])

  return {
    data,
    setData,
    loading,
    error,
    loaderRef,
    reset,
    retry: loadMore
  }
}
