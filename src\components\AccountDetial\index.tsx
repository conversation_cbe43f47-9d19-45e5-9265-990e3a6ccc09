import React, { useEffect, useMemo } from 'react'
import { <PERSON><PERSON>, But<PERSON>, Typography, Tabs } from 'antd'
import { LeftOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import { accountDetailStore } from '@/store'
import { deleteAccountTrace } from '@/services/accountTrace'
import styles from './index.module.less'
import AccountOverview from './components/AccountOverview'
import AccountAnalysis from './components/AccountAnalysis'
import AllWorks from './components/AllWorks'
import AccountInfo from './components/AccountInfo'
import { message, modal } from '../GlobalTips'
import { v4 as uuidv4 } from 'uuid'
import FloatBackTop from '../FloatBackTop'

const { Title } = Typography

interface AccountDetailProps {
  open: boolean
  onDelete: () => void
  onClose: () => void
  atUniqueId: string
}

const AccountDetail: React.FC<AccountDetailProps> = ({ open, onClose, atUniqueId, onDelete }) => {
  const handleDelete = () => {
    modal.confirm({
      title: '移除监控',
      content: `确定要移除"${accountDetailStore!.accountDetailData!.name}"吗？移除后将无法恢复。`,
      okText: '确定',
      cancelText: '取消',
      okButtonProps: {
        danger: true
      },
      closable: true,
      onOk: () => {
        return deleteAccountTrace({ atUniqueId })
          .then(res => {
            if (res.code === 'success') {
              message.success('删除成功')
              onDelete()
            } else {
              message.error(res.msg || '删除失败')
            }
          })
          .catch(() => {
            message.error('删除失败')
          })
      }
    })
  }

  useEffect(() => {
    if (open && atUniqueId) {
      accountDetailStore.atUniqueId = atUniqueId
      accountDetailStore.fetchAccountDetail()
    }
    return () => {
      accountDetailStore.reset()
    }
  }, [open, atUniqueId])

  const refreshKey = useMemo(() => {
    return uuidv4()
  }, [accountDetailStore.refresh])

  const handleTabChange = (key: string) => {
    accountDetailStore.setActiveTab(key)
  }

  // Ant Design v5 Tabs items 配置
  const tabItems = [
    {
      key: 'overview',
      label: '账号总览',
      children: <AccountOverview />
    },
    {
      key: 'analysis',
      label: '账号拆解AI',
      children: <AccountAnalysis />
    },
    {
      key: 'works',
      label: '全部作品',
      children: <AllWorks />
    }
  ]

  return (
    <Drawer
      loading={accountDetailStore.loading}
      title={null}
      placement="right"
      onClose={onClose}
      open={open}
      closable={false}
      width="100%"
      styles={{
        body: {
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          padding: '56px 0 12px',
          overflow: 'hidden',
          height: '100vh',
          background:
            'linear-gradient(to top, rgb(50, 102, 255), rgb(185, 203, 255), rgb(244, 245, 255))'
        }
      }}
      destroyOnClose
    >
      {/* 顶部导航 */}
      <div className={styles.topNav}>
        <Button type="text" icon={<LeftOutlined />} onClick={onClose} className={styles.backButton}>
          返回
        </Button>
        <Title level={4} className={styles.pageTitle} style={{ marginBottom: 0 }}>
          账号详情
        </Title>
        <div className={styles.accountActions}>
          <Button type="primary" onClick={handleDelete}>
            移除监控账号
          </Button>
        </div>
      </div>
      <div className={styles.accountDetail} id="accountDetail" key={refreshKey}>
        {/* 账号信息区域 */}
        <AccountInfo />

        {/* 标签页区域 */}
        <div className={styles.tabsContainer}>
          <Tabs
            activeKey={accountDetailStore.activeTab}
            onChange={handleTabChange}
            className={styles.accountTabs}
            items={tabItems}
            renderTabBar={(props, DefaultTabBar) => (
              <DefaultTabBar {...props} className={styles.accountTabsBar} />
            )}
          />
        </div>
        <FloatBackTop id="accountDetail" />
      </div>
    </Drawer>
  )
}

export default observer(AccountDetail)
