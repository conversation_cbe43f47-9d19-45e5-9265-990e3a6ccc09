import { useEffect, useRef, useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import NProgress from 'nprogress'
import { useAsyncEffect } from 'ahooks'
import BasicLayout from './layout'
import { authStore, globalStore } from './store'
import { authInfoStorage } from './utils/storage'
NProgress.configure({ showSpinner: false })

const App = () => {
  const [infoChecking, setInfoChecking] = useState(true)
  const navigate = useNavigate()
  const location = useLocation()
  const force = useRef(true)
  useEffect(() => {
    authInfoStorage.addEventListener(data => {
      if (!data) {
        navigate(`/login?redirect=${encodeURIComponent(location.pathname + location.search)}`)
        window.location.reload()
        localStorage.removeItem('collapsed')
      }
    }, force.current)
    return () => {
      authInfoStorage && authInfoStorage.removeEventListener()
    }
  }, [location.pathname])

  useAsyncEffect(async () => {
    NProgress.start()
    const res = await authStore.getUserDetail()
    NProgress.done()
    force.current = false
    if (!res) {
      authInfoStorage.remove()
    } else {
      globalStore.getMaskCloudImage()
      setInfoChecking(false)
    }
  }, [])

  if (infoChecking) {
    return null
  }

  return <BasicLayout />
}

export default App
