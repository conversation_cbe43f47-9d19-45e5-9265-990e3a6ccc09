import React, { useState, useEffect } from 'react'
import { Tag, Space, DatePicker } from 'antd'
import dayjs from 'dayjs'
import { PlatFormOptions } from '@/constants'

const timeOptions = [
  { label: '全部', value: 'all' },
  { label: '近7日', value: '7d' },
  { label: '近14日', value: '14d' },
  { label: '近30日', value: '30d' },
  { label: '自定义', value: 'custom' }
]

const { RangePicker } = DatePicker

export interface FilterBarValue {
  platform: string
  time: string
  customRange: any
}

interface FilterBarProps {
  value: FilterBarValue
  onChange: (val: FilterBarValue) => void
}

const FilterBar: React.FC<FilterBarProps> = ({ value, onChange }) => {
  const [platform, setPlatform] = useState(value.platform)
  const [time, setTime] = useState(value.time)
  const [customRange, setCustomRange] = useState<any>(value.customRange)
  const [showRange, setShowRange] = useState(false)

  // 只要RangePicker有值且选择了日期，所有Tag都不高亮
  const isCustomSelected = !!customRange && customRange[0] && customRange[1]

  // 处理RangePicker变化
  const handleRangeChange = (val: any) => {
    setCustomRange(val)
    if (!val || !val[0] || !val[1]) {
      // 清空日期选择时，回到"全部"状态
      setShowRange(false)
      setTime('all')
    } else {
      // 选择了具体日期时，设置为自定义状态并触发接口调用
      setShowRange(true)
      setTime('custom')
    }
  }

  // 处理自定义按钮点击
  const handleCustomClick = () => {
    setShowRange(true)
    // 不改变 time 状态，保持之前的筛选条件
  }

  // 处理其他时间选项点击
  const handleTimeOptionClick = (optionValue: string) => {
    setTime(optionValue)
    setCustomRange(null)
    setShowRange(false)
  }

  // 联动外部 - 只有在实际状态改变时才触发
  useEffect(() => {
    onChange({ platform, time, customRange })
  }, [platform, time, customRange])

  // 获取日期选择的禁用日期函数
  const disabledDate = (current: any) => {
    // 禁用今天之后的日期和一个月之前的日期
    const today = dayjs()
    const oneMonthAgo = today.subtract(1, 'month')
    return current && (current > today || current < oneMonthAgo)
  }

  return (
    <div style={{ background: '#fff', padding: 16, borderRadius: 8 }}>
      {/* 平台筛选 */}
      <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
        <div style={{ marginRight: 8, fontWeight: 500, width: 80 }}>平台：</div>
        <Space size={12} wrap style={{ flex: 1 }}>
          {PlatFormOptions.map(opt => (
            <Tag.CheckableTag
              className="selectTag"
              key={opt.value}
              checked={platform === opt.value}
              onChange={() => setPlatform(opt.value)}
            >
              <Space size={12}>
                {opt.icon && opt.icon}
                {opt.label}
              </Space>
            </Tag.CheckableTag>
          ))}
        </Space>
      </div>
      {/* 时间筛选 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div style={{ marginRight: 8, fontWeight: 500, width: 80 }}>时间：</div>
        <Space size={12} wrap style={{ flex: 1 }}>
          {timeOptions.map(opt => {
            if (opt.value === 'custom') {
              // 只替换自定义按钮为RangePicker
              return showRange ? (
                <RangePicker
                  key="rangepicker"
                  allowClear
                  style={{ marginLeft: 8, height: 26 }}
                  value={customRange}
                  onChange={handleRangeChange}
                  disabledDate={disabledDate}
                  placeholder={['开始日期', '结束日期']}
                />
              ) : (
                <Tag.CheckableTag
                  key={opt.value}
                  checked={false}
                  onChange={handleCustomClick}
                  className="selectTag"
                >
                  {opt.label}
                </Tag.CheckableTag>
              )
            }
            // 只有当自定义日期范围选择完成时，其他Tag才不高亮
            return (
              <Tag.CheckableTag
                className="selectTag"
                key={opt.value}
                checked={!isCustomSelected && time === opt.value}
                onChange={() => handleTimeOptionClick(opt.value)}
              >
                {opt.label}
              </Tag.CheckableTag>
            )
          })}
        </Space>
      </div>
    </div>
  )
}

export default FilterBar
