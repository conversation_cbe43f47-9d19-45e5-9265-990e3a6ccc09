import Icon from '@ant-design/icons'
import type { GetProps } from 'antd'
type CustomIconComponentProps = GetProps<typeof Icon>

/**
 * 直播
 */
export const LiveIcon = (props: CustomIconComponentProps) => (
  <Icon
    component={() => (
      <svg
        className="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
      >
        <path
          d="M0 512c0 282.624 229.376 512 512 512s512-229.376 512-512S794.624 0 512 0 0 229.376 0 512z"
          fill="#130C19"
        ></path>
        <path
          d="M600.064 256c139.264 0 252.928 114.688 252.928 256s-112.64 256-252.928 256H222.208c-5.12 0-9.216-4.096-9.216-9.216 0-2.048 0-3.072 1.024-5.12l10.24-17.408 152.576-257.024c1.024-2.048 3.072-3.072 5.12-4.096 1.024-1.024 2.048-1.024 3.072-2.048h82.944c3.072 0 6.144 2.048 8.192 5.12l44.032 74.752 24.576-41.984 34.816-58.368-31.744-19.456c-3.072-2.048-4.096-6.144-2.048-10.24 1.024-1.024 2.048-2.048 4.096-3.072l109.568-32.768c4.096-1.024 7.168 1.024 8.192 5.12v1.024l22.528 112.64c1.024 4.096-2.048 7.168-5.12 8.192-2.048 0-3.072 0-5.12-1.024l-29.696-18.432-84.992 143.36c-1.024 1.024-2.048 3.072-3.072 3.072-2.048 2.048-4.096 4.096-7.168 4.096h-68.608c-3.072 0-6.144-2.048-8.192-4.096l-51.2-86.016L349.184 686.08h250.88c95.232 0 172.032-77.824 172.032-174.08s-76.8-174.08-172.032-174.08H377.856c-5.12 0-9.216-4.096-9.216-9.216 0-2.048 0-3.072 1.024-5.12l37.888-63.488c1.024-2.048 4.096-4.096 7.168-4.096h185.344z"
          fill="#FF0650"
        ></path>
        <path
          d="M558.08 256c139.264 0 252.928 114.688 252.928 256S697.344 768 558.08 768H180.224c-5.12 0-9.216-4.096-9.216-9.216 0-2.048 0-3.072 1.024-5.12l10.24-17.408 152.576-257.024c1.024-2.048 3.072-3.072 5.12-4.096 1.024-1.024 2.048-2.048 3.072-2.048h82.944c3.072 0 6.144 2.048 8.192 5.12l44.032 74.752 24.576-41.984 34.816-58.368-31.744-19.456c-3.072-2.048-4.096-6.144-2.048-10.24 1.024-1.024 2.048-2.048 4.096-3.072l108.544-32.768c4.096-1.024 7.168 1.024 8.192 5.12v1.024l22.528 112.64c1.024 4.096-2.048 7.168-5.12 8.192-2.048 0-3.072 0-5.12-1.024l-29.696-18.432-84.992 143.36c-1.024 1.024-2.048 3.072-3.072 3.072-2.048 2.048-4.096 4.096-7.168 4.096h-68.608c-3.072 0-6.144-2.048-8.192-4.096l-51.2-86.016L306.176 686.08h250.88c95.232 0 172.032-77.824 172.032-174.08s-76.8-174.08-172.032-174.08H334.848c-5.12 0-9.216-4.096-9.216-9.216 0-2.048 0-3.072 1.024-5.12l37.888-63.488c1.024-2.048 4.096-4.096 7.168-4.096h186.368z"
          fill="#7AF9FF"
        ></path>
        <path
          d="M600.064 686.08c95.232 0 172.032-77.824 172.032-174.08s-76.8-174.08-172.032-174.08H377.856c-5.12 0-9.216-4.096-9.216-9.216 0-2.048 0-3.072 1.024-5.12l37.888-63.488c1.024-2.048 4.096-4.096 7.168-4.096h143.36c139.264 0 252.928 114.688 252.928 256S697.344 768 558.08 768H222.208c-5.12 0-9.216-4.096-9.216-9.216 0-2.048 0-3.072 1.024-5.12l10.24-17.408 152.576-257.024c1.024-2.048 3.072-3.072 5.12-4.096 1.024-1.024 2.048-1.024 3.072-2.048h40.96c3.072 0 6.144 2.048 8.192 5.12l44.032 74.752 21.504-35.84 21.504 35.84 24.576-41.984 34.816-58.368-31.744-19.456c-3.072-2.048-4.096-6.144-2.048-10.24 1.024-1.024 2.048-2.048 4.096-3.072l75.776-22.528 20.48 102.4-8.192 13.312s-1.024 0-1.024-1.024l-29.696-18.432-84.992 143.36-3.072 3.072c-2.048 2.048-4.096 4.096-7.168 4.096h-25.6c-3.072 0-6.144-2.048-8.192-4.096l-51.2-86.016-21.504 35.84-21.504-35.84L306.176 686.08h293.888z"
          fill="#FFFFFF"
        ></path>
      </svg>
    )}
    {...props}
  />
)

/**
 * 餐饮
 */
export const RepastIcon = (props: CustomIconComponentProps) => (
  <Icon
    component={() => (
      <svg
        className="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
      >
        <path
          d="M512 0C228.693333 0 0 228.693333 0 512s228.693333 512 512 512 512-228.693333 512-512S795.306667 0 512 0z m303.786667 375.466667c-23.893333 30.72-47.786667 47.786667-71.68 75.093333-58.026667 61.44-78.506667 61.44-88.746667 61.44h-3.413333c-17.066667 0-30.72-3.413333-40.96-13.653333l-20.48 30.72 180.906666 187.733333c6.826667 10.24 13.653333 23.893333 10.24 37.546667 0 13.653333-6.826667 23.893333-13.653333 30.72-10.24 6.826667-20.48 10.24-30.72 10.24-10.24 0-20.48-3.413333-30.72-13.653334-6.826667-6.826667-105.813333-122.88-167.253333-191.146666l-177.493334 191.146666c-10.24 10.24-20.48 13.653333-34.133333 13.653334s-20.48 0-34.133333-10.24c-10.24-6.826667-13.653333-17.066667-13.653334-30.72-3.413333-13.653333 3.413333-30.72 10.24-40.96 10.24-10.24 153.6-150.186667 187.733334-180.906667l-13.653334-13.653333c-10.24 3.413333-75.093333-3.413333-92.16-20.48-13.653333-17.066667-98.986667-143.36-98.986666-201.386667 0-13.653333 6.826667-37.546667 30.72-37.546667 13.653333 0 30.72 10.24 54.613333 34.133334 13.653333 13.653333 150.186667 153.6 180.906667 187.733333l17.066666-20.48c-10.24-10.24-17.066667-20.48-23.893333-34.133333-3.413333-13.653333 0-30.72 6.826667-40.96l3.413333-6.826667c37.546667-47.786667 81.92-95.573333 129.706667-133.12 6.826667-6.826667 13.653333-6.826667 23.893333-6.826667s13.653333 6.826667 17.066667 13.653334c3.413333 6.826667 3.413333 13.653333 0 20.48-6.826667 6.826667-95.573333 102.4-105.813334 112.64-3.413333 3.413333-10.24 20.48-6.826666 20.48h3.413333c6.826667 0 10.24-3.413333 13.653333-6.826667 6.826667-6.826667 95.573333-98.986667 102.4-105.813333 3.413333-3.413333 10.24-6.826667 17.066667-6.826667 10.24 0 17.066667 6.826667 20.48 17.066667 3.413333 6.826667 3.413333 17.066667-3.413333 20.48-6.826667 10.24-98.986667 109.226667-102.4 116.053333-3.413333 3.413333-6.826667 6.826667-3.413334 13.653333l3.413334 3.413334c6.826667-3.413333 10.24-3.413333 13.653333-6.826667 3.413333-6.826667 95.573333-98.986667 105.813333-105.813333 3.413333-3.413333 10.24-6.826667 13.653334-6.826667 13.653333 0 23.893333 6.826667 30.72 17.066667 3.413333 6.826667 3.413333 17.066667 0 20.48z"
          fill="#FA8D14"
        ></path>
      </svg>
    )}
    {...props}
  />
)

/**
 * 系统
 */
export const SystemIcon = (props: CustomIconComponentProps) => (
  <Icon
    component={() => (
      <svg
        className="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
      >
        <path
          d="M371.7632 56.32H138.24a81.5616 81.5616 0 0 0-81.92 81.92v233.5232a81.5616 81.5616 0 0 0 81.92 81.92h233.5232a81.92 81.92 0 0 0 81.92-81.92V138.24a81.92 81.92 0 0 0-81.92-81.92zM371.7632 570.4192H138.24a81.5616 81.5616 0 0 0-81.92 81.92V885.76a81.5616 81.5616 0 0 0 81.92 81.92h233.5232a81.92 81.92 0 0 0 81.92-81.92v-233.5232a81.92 81.92 0 0 0-81.92-81.8176zM767.1808 456.8576a200.2944 200.2944 0 1 0-200.0384-200.0384 200.6528 200.6528 0 0 0 200.0384 200.0384z m0-330.4448a130.7648 130.7648 0 0 1 130.4064 129.9456 130.2016 130.2016 0 1 1-130.4064-129.9456zM885.76 570.4192h-233.5232a81.6128 81.6128 0 0 0-81.92 81.92V885.76a81.5616 81.5616 0 0 0 81.92 81.92H885.76a81.92 81.92 0 0 0 81.92-81.92v-233.5232a81.92 81.92 0 0 0-81.92-81.8176z"
          fill="#1296db"
        ></path>
      </svg>
    )}
    {...props}
  />
)

/**
 * 小红书
 */
export const RedBookIcon = (props: CustomIconComponentProps) => (
  <Icon
    component={() => (
      <svg
        className="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
      >
        <path
          d="M1021.72444445 836.54883555V187.48757333C1021.72444445 85.61550222 938.38449778 2.27555555 836.51242667 2.27555555H187.48757333C85.61550222 2.27555555 2.27555555 85.61550222 2.27555555 187.48757333v649.06126222c0 100.85262222 81.70154667 183.57361778 182.2264889 185.1756089h654.9959111c100.48853333-1.60199111 182.22648889-84.28657778 182.2264889-185.1756089"
          fill="#FF2442"
        ></path>
        <path
          d="M726.52117333 366.36444445h57.344v20.53461333c0 1.6384 0.80099555 2.40298667 2.36657778 2.36657777 34.00590222-1.01944889 68.26666667 0.07281778 85.81575111 34.95253334 10.44935111 20.68024889 8.30122667 52.13752889 7.71868445 76.82275556-0.03640889 1.45635555 0.65536 2.25735111 2.03889778 2.40298666 4.00497778 0.36408889 7.90072889 0.72817778 11.68725333 1.20149334 67.61130667 8.11918222 54.24924445 71.87114667 54.46769777 121.96977777 0.10922667 17.47626667-1.85685333 30.25578667-5.82542222 38.41137778-8.37404445 16.89372445-23.37450667 26.57848889-45.00138666 28.98147555H854.97173333l-21.55406222-50.02581333a1.6384 1.6384 0 0 1 0.10922667-1.52917333 1.56558222 1.56558222 0 0 1 1.31072-0.72817778l45.72956444-0.03640889c2.54862222 0 4.95160889-1.09226667 6.69923556-2.98552889a10.12167111 10.12167111 0 0 0 2.69425777-7.02691555c-0.21845333-15.29173333-0.32768-30.54705778-0.25486222-45.80238223 0-13.72615111-6.48078222-20.75306667-19.55157333-21.11715555-14.78200889-0.36408889-42.78044445-0.36408889-84.03171555 0.07281778-1.45635555 0-2.18453333 0.80099555-2.18453334 2.36657777l-0.21845333 126.81216H726.44835555l-0.18204444-127.35829333a2.25735111 2.25735111 0 0 0-2.22094222-2.33016889h-53.52106667a2.54862222 2.54862222 0 0 1-2.47580444-2.54862222l0.07281777-55.41432889c0-1.85685333 0.87381333-2.80348445 2.62144-2.80348444l52.90211556 0.10922667a2.51221333 2.51221333 0 0 0 1.82044444-0.80099556 2.76707555 2.76707555 0 0 0 0.72817778-1.89326222v-47.91409778a3.16757333 3.16757333 0 0 0-3.05834666-3.24039111l-32.65877334 0.14563555c-1.71121778 0-2.54862222-0.91022222-2.54862222-2.69425778l-0.10922666-55.7056c0-1.6384 0.72817778-2.43939555 2.36657777-2.43939555h33.82385778c1.45635555 0 2.18453333-0.72817778 2.18453333-2.29376l0.36408889-20.46179555z m59.38289778 137.37073777l35.57148444-0.07281777c0.58254222 0 1.12867555-0.25486222 1.52917334-0.6917689a2.29376 2.29376 0 0 0 0.61895111-1.6019911l-0.18204445-44.52807112c0-3.49525333-2.54862222-6.33514667-5.64337777-6.33514666l-28.54456889 0.07281778a5.35210667 5.35210667 0 0 0-4.00497778 1.89326222 6.80846222 6.80846222 0 0 0-1.6384 4.55111111l0.18204444 44.52807111c0 1.23790222 0.98304 2.18453333 2.11171556 2.18453333zM417.95584 507.74016c-13.83537778 0.25486222-38.84828445 4.11420445-44.30961778-13.68974222-3.31320889-10.63139555 4.18702222-25.44981333 8.73813333-35.82634667 12.96156445-29.52760889 25.66826667-59.16444445 38.15651556-88.91050666 0.50972445-1.20149333 1.38353778-1.82044445 2.62144-1.82044445h54.72256c0.47331555 0 0.87381333 0.25486222 1.09226667 0.65536a1.45635555 1.45635555 0 0 1 0.14563555 1.31072l-31.67573333 74.01927111c-0.72817778 1.71121778-0.54613333 3.64088889 0.40049778 5.24288a5.17006222 5.17006222 0 0 0 4.36906667 2.47580444h46.89464888c0.58254222 0 1.09226667 0.29127111 1.41994667 0.76458667 0.29127111 0.50972445 0.36408889 1.09226667 0.10922667 1.6384-13.54410667 31.56650667-27.05180445 62.91456-40.52309334 94.04416-1.34712889 3.09475555-1.92967111 5.38851555-1.71121778 6.84487111 0.47331555 3.16757333 2.25735111 4.76956445 5.31569778 4.80597334l29.67324445 0.18204444c1.71121778 0.03640889 2.25735111 0.87381333 1.56558222 2.54862222l-19.18748445 45.14702222a3.78652445 3.78652445 0 0 1-3.64088888 2.51221334c-30.14656 0.36408889-51.22730667 0.36408889-63.24224-0.18204444-19.87925333-0.91022222-24.75804445-18.31367111-17.03936-36.26325334l27.27025778-63.64273778a1.38353778 1.38353778 0 0 0-0.10922667-1.23790222 1.23790222 1.23790222 0 0 0-1.09226667-0.61895111zM190.58232889 694.00803555h-21.48124444l-21.04433778-49.40686222a1.60199111 1.60199111 0 0 1 0.10922666-1.49276444 1.45635555 1.45635555 0 0 1 1.23790222-0.72817778l29.70965334-0.07281778a6.95409778 6.95409778 0 0 0 6.80846222-7.09973333l0.80099556-262.03477333a2.54862222 2.54862222 0 0 1 2.51221333-2.62144h51.11808c2.40298667 0 3.60448 1.27431111 3.64088889 3.78652444 0.21845333 88.72846222 0.21845333 175.92775111 0 261.63427556-0.14563555 35.17098667-16.45681778 59.20085333-53.41184 58.03576888z"
          fill="#FFFFFF"
        ></path>
        <path
          d="M670.08739555 694.00803555h-193.91374222l25.99594667-58.6183111a3.45884445 3.45884445 0 0 1 3.38602667-2.22094223l47.47719111 0.07281778c1.67480889 0 2.54862222-0.83740445 2.54862222-2.54862222v-177.85742223c0-1.52917333-0.72817778-2.29376-2.18453333-2.29376l-31.49368889-0.03640888c-1.41994667 0-2.54862222-1.23790222-2.54862223-2.73066667v-57.05272889c0-0.87381333 0.65536-1.60199111 1.49276445-1.60199111h128.37774222c1.60199111 0 2.36657778 0.83740445 2.36657778 2.51221333l0.07281778 56.43377778c0 1.6384-0.80099555 2.47580445-2.40298667 2.47580444h-31.74855111c-1.45635555 0-2.18453333 0.76458667-2.18453333 2.29376v177.74819556c0 1.71121778 0.83740445 2.54862222 2.43939555 2.54862222l50.31708445 0.10922667c1.38353778 0 2.07530667 0.72817778 2.07530666 2.18453333L670.08739555 694.04444445zM901.02897778 394.65415111c39.61287111-27.23384889 67.50208 42.19790222 24.10268444 54.10360889-7.06332445 1.96608-18.31367111 2.07530667-33.71463111 0.36408889-1.38353778-0.14563555-2.03889778-0.91022222-2.03889778-2.36657778-0.21845333-16.384-3.45884445-41.72458667 11.65084445-52.06471111zM354.20387555 598.79879111l-26.2144 61.05770667c-2.36657778 5.46133333-4.95160889 5.57056-7.8279111 0.43690667-19.29671111-34.87971555-25.85031111-63.35146667-29.63683556-106.71445334-2.91271111-33.67822222-5.42492445-67.35644445-7.60945778-101.10748444-0.07281778-1.52917333 0.61895111-2.29376 2.07530667-2.29376l53.12056889 0.03640888c1.49276445 0 2.33016889 0.80099555 2.43939555 2.3301689 2.73066667 39.24878222 5.60696889 78.38833778 8.59249778 117.41866666 0.76458667 10.04885333 2.47580445 18.38648889 5.09724445 25.01290667a4.73315555 4.73315555 0 0 1-0.0364089 3.82293333zM75.09333333 596.54144v-2.51221333a25.70467555 25.70467555 0 0 0 4.73315556-11.50520889c3.93216-43.32657778 7.13614222-86.61674667 9.64835556-129.94332445 0.10922667-1.34712889 0.76458667-2.03889778 2.03889777-2.03889778h54.24924445c0.47331555 0 0.94663111 0.21845333 1.31072 0.61895112 0.32768 0.36408889 0.50972445 0.87381333 0.47331555 1.38353778a7226.07217778 7226.07217778 0 0 1-9.57553777 119.67601777c-2.54862222 28.94506667-11.79648 67.68412445-31.1296 91.16785778-1.23790222 1.49276445-2.29376 1.34712889-3.09475556-0.47331555L75.09333333 596.54144zM445.08046222 694.00803555h-78.57038222l-10.01244445-3.96856888c-1.41994667-0.54613333-1.82044445-1.52917333-1.16508444-2.94912l24.64881778-56.43377778c0.72817778-1.6384 1.89326222-2.25735111 3.56807111-1.82044444 26.94257778 7.31818667 58.14499555 4.29624889 85.70652445 4.40547555 1.71121778 0.03640889 2.18453333 0.87381333 1.45635555 2.47580445l-25.63185778 58.25422222z"
          fill="#FFFFFF"
        ></path>
      </svg>
    )}
    {...props}
  />
)

/**
 * 视频号
 */
export const VideoNumberIcon = (props: CustomIconComponentProps) => (
  <Icon
    component={() => (
      <svg
        className="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
      >
        <path
          d="M282.04032 310.272c-2.96448 2.0736-9.33376 10.496-12.62592 32.22016-4.83328 31.85664-3.3792 95.90784 31.0016 220.20096 1.03936 3.4304 11.49952 37.6064 26.01472 73.73824 21.36064 53.18144 35.40992 71.08608 40.92928 76.61568 8.04864-3.29216 26.66496-15.33952 53.02272-47.40096 23.0656-28.0576 43.73504-61.53216 56.9856-91.9552-13.38368-23.23968-43.50976-74.47552-77.92128-126.04928C320.77312 329.74336 288.33792 312.6272 282.04032 310.272zM741.95968 310.272c-6.2976 2.3552-38.7328 19.47136-117.40672 137.3696-34.41152 51.57376-64.53248 102.8096-77.92128 126.04928 13.25568 30.42304 33.92 63.89248 56.9856 91.9552 26.35776 32.06144 44.9792 44.1088 53.02784 47.40096 5.52448-5.54496 19.63008-23.52128 41.08288-77.00992a985.216 985.216 0 0 0 25.85088-73.33888c34.37568-124.29312 35.83488-188.34944 31.0016-220.20608-3.29216-21.72416-9.66144-30.15168-12.6208-32.22016z"
          fill="#FA9D3B"
        ></path>
        <path
          d="M849.92 51.2H174.08c-67.8656 0-122.88 55.0144-122.88 122.88v675.84c0 67.8656 55.0144 122.88 122.88 122.88h675.84c67.8656 0 122.88-55.0144 122.88-122.88V174.08c0-67.8656-55.0144-122.88-122.88-122.88z m-66.21184 528.50688l-0.1792 0.62976c-0.54784 1.83296-13.71136 45.57312-32.3584 90.07104-31.17568 74.37824-58.48064 106.11712-91.29472 106.11712-29.96736 0-64.49152-23.17824-102.61504-68.89472-16.52224-19.80928-32.03072-42.14784-45.2608-64.79872-13.23008 22.65088-28.73856 44.98944-45.2608 64.79872-38.12352 45.71648-72.64768 68.89472-102.61504 68.89472-32.81408 0-60.11904-31.73376-91.29472-106.11712-18.65216-44.4928-31.80544-88.23296-32.3584-90.07104l-0.1792-0.62976c-43.4944-157.11744-46.976-256.70656-10.65472-304.45056 19.60448-25.76896 44.35456-27.77088 51.47136-27.77088 0.54784 0 1.08544 0.01024 1.6128 0.03072 28.80512 0.38912 71.168 20.4288 165.12 160.11264 24.96512 37.12 47.62624 73.90208 64.1536 101.59104 16.53248-27.68896 39.18848-64.46592 64.1536-101.59104 93.952-139.68384 136.31488-159.72352 165.12-160.11264 0.52736-0.02048 1.06496-0.03072 1.6128-0.03072 7.1168 0 31.86688 2.00704 51.47136 27.77088 36.33152 47.744 32.84992 147.33312-10.64448 304.45056z"
          fill="#FA9D3B"
        ></path>
      </svg>
    )}
    {...props}
  />
)
/**
 * 品牌洞察
 */
export const BrandInsights = (props: CustomIconComponentProps) => (
  <Icon
    component={() => (
      <svg
        className="icon"
        viewBox="0 0 1144 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
      >
        <path
          d="M1049.057882 176.308706c-2.288941-5.12-7.408941-9.637647-9.637647-17.046588C939.489882-13.191529 893.530353 0.963765 735.232 0.963765H399.480471c-158.298353 0-204.197647-11.866353-304.067765 158.298353-2.288941 5.12-7.408941 9.637647-9.637647 17.046588-87.401412 153.118118-138.480941 233.712941 0 396.528941l264.914823 272.263529c96.978824 99.870118 153.178353 160.587294 216.124236 158.298353 62.945882 0 119.145412-58.428235 216.124235-158.238117l264.975059-272.323765c139.565176-163.418353 88.485647-243.350588 1.14447-396.528941zM803.358118 455.981176l-36.261647 39.152942c-39.152941 39.152941-68.065882 70.294588-96.978824 92.461176-34.093176 26.624-65.837176 41.381647-102.159059 41.381647s-68.065882-14.757647-102.098823-41.381647c-28.912941-21.564235-58.428235-53.368471-96.978824-92.461176l-36.382117-39.152942a47.284706 47.284706 0 0 1 2.288941-68.065882 47.284706 47.284706 0 0 1 68.065882 2.228706l36.321882 39.152941c34.032941 36.321882 62.945882 65.837176 87.341177 85.112471 17.046588 14.757647 31.804235 21.564235 41.441882 21.564235 11.926588 0 24.395294-7.408941 41.381647-21.564235 24.395294-19.275294 51.079529-48.790588 87.341177-85.112471l36.382117-39.152941a49.392941 49.392941 0 0 1 68.065883-2.228706c19.275294 19.275294 21.564235 48.188235 2.228706 68.065882z"
          fill="#d81e06"
        ></path>
      </svg>
    )}
    {...props}
  />
)

/**
 * 数据大盘
 */
export const MarketIcon = (props: CustomIconComponentProps) => (
  <Icon
    component={() => (
      <svg
        className="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
      >
        <path
          d="M860.936147 0H163.073852A98.543264 98.543264 0 0 0 64.530589 98.543264v602.798793a98.533264 98.533264 0 0 0 98.543263 98.533264h697.862295A98.533264 98.533264 0 0 0 959.469411 701.342057V98.543264A98.533264 98.533264 0 0 0 860.936147 0zM739.674436 348.666165l-143.94016 155.989337c-0.559962 0.72995-1.139922 1.449901-1.75988 2.149853a41.507163 41.507163 0 0 1-58.585995 3.649751l-108.642573-95.873447-79.894539 83.724277a41.507163 41.507163 0 0 1-58.685988 1.369906 41.507163 41.507163 0 0 1-1.369906-58.685988l104.992823-110.052477c0.399973-0.489967 0.809945-0.999932 1.229916-1.469899a42.297109 42.297109 0 0 1 60.385872-2.999795 41.217182 41.217182 0 0 1 4.08972 4.519691l102.273009 90.273829 118.911871-128.891189a41.497163 41.497163 0 0 1 58.645991-2.359839 41.507163 41.507163 0 0 1 2.349839 58.65599z"
          fill="#4294F7"
        ></path>
        <path
          d="M191.161932 940.46571m41.767145 0l556.541955 0q41.767145 0 41.767145 41.767145l0 0q0 41.767145-41.767145 41.767145l-556.541955 0q-41.767145 0-41.767145-41.767145l0 0q0-41.767145 41.767145-41.767145Z"
          fill="#4294F7"
        ></path>
      </svg>
    )}
    {...props}
  />
)

/**
 * 第一
 */
export const FirstIcon = (props: CustomIconComponentProps) => (
  <Icon
    component={() => (
      <svg
        className="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
      >
        <path
          d="M239.662545 638.370909v363.845818L512 888.925091l274.525091 113.291636V638.370909z"
          fill="#B160E4"
        ></path>
        <path
          d="M239.662545 745.122909A429.335273 429.335273 0 0 0 512 843.170909c104.587636 0 198.283636-37.050182 272.337455-98.048V638.370909H237.474909l2.187636 106.752z"
          fill="#994EC8"
        ></path>
        <path
          d="M124.183273 387.816727c0 214.178909 173.614545 387.816727 387.816727 387.816728 214.178909 0 387.816727-173.637818 387.816727-387.816728C899.816727 173.614545 726.202182 0 512 0 297.821091 0 124.183273 173.614545 124.183273 387.816727z"
          fill="#FFC845"
        ></path>
        <path
          d="M215.691636 387.816727c0 163.630545 132.654545 296.308364 296.308364 296.308364 163.653818 0 296.308364-132.654545 296.308364-296.308364 0-163.653818-132.654545-296.308364-296.308364-296.308363-163.653818 0-296.308364 132.654545-296.308364 296.308363z"
          fill="#FFDC17"
        ></path>
        <path
          d="M542.487273 224.418909V566.458182h-56.622546V291.956364c-21.806545 19.595636-45.754182 32.674909-78.429091 41.378909v-54.458182c15.243636-4.352 30.487273-10.891636 50.106182-19.618909 17.431273-10.891636 30.487273-21.783273 43.566546-32.674909h41.402181v-2.164364z"
          fill="#FF9600"
        ></path>
      </svg>
    )}
    style={{ fontSize: '32px' }}
    {...props}
  />
)

/**
 * 第二
 */
export const SecondIcon = (props: CustomIconComponentProps) => (
  <Icon
    component={() => (
      <svg
        className="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
      >
        <path
          d="M218.4 594.4L276 652c6.4 6.4 10.4 16 10.4 25.6v244.8c0 13.6 14.4 22.4 26.4 15.2l180.8-102.4c11.2-7.2 26.4-7.2 37.6 0l180 100.8c12 7.2 27.2-0.8 27.2-15.2V677.6c0-9.6 4-19.2 10.4-25.6l57.6-57.6c6.4-6.4 10.4-16 10.4-25.6v-27.2c0-20-16-36-36-36H244c-20 0-36 16-36 36v27.2c0 9.6 3.2 18.4 10.4 25.6z"
          fill="#FF7378"
        ></path>
        <path
          d="M488.8 719.2L237.6 584.8c-18.4-9.6-30.4-28.8-30.4-49.6V274.4c0-20.8 11.2-40 30.4-49.6L488.8 90.4c14.4-8 32.8-8 47.2 0l250.4 134.4c18.4 9.6 30.4 28.8 30.4 49.6v259.2c0 20.8-11.2 40-30.4 49.6L535.2 717.6c-14.4 9.6-32 9.6-46.4 1.6z"
          fill="#FFBBC2"
        ></path>
        <path
          d="M479.2 656.8L276.8 547.2c-11.2-5.6-17.6-17.6-17.6-29.6V284c0-12 7.2-23.2 17.6-29.6l202.4-109.6c20.8-11.2 44.8-11.2 65.6 0l202.4 109.6c11.2 5.6 17.6 17.6 17.6 29.6v234.4c0 12-7.2 23.2-17.6 29.6L544.8 656.8c-20 11.2-44.8 11.2-65.6 0z"
          fill="#FFEEF1"
        ></path>
        <path
          d="M590.4 272.8c19.2 15.2 28 35.2 28 59.2 0 23.2-10.4 44.8-30.4 64.8-12 11.2-33.6 25.6-64.8 44-33.6 19.2-53.6 36.8-60 52h156v36.8H404.8c0-27.2 10.4-51.2 31.2-72 10.4-11.2 34.4-28 70.4-49.6 21.6-13.6 36-24 44.8-31.2 14.4-14.4 21.6-28.8 21.6-44.8 0-15.2-4.8-27.2-14.4-34.4s-24-11.2-42.4-11.2c-20 0-34.4 5.6-44.8 17.6-10.4 11.2-16 28-16.8 50.4h-47.2c0.8-30.4 10.4-55.2 30.4-73.6 20-20 46.4-29.6 80-29.6 29.6-0.8 54.4 6.4 72.8 21.6z"
          fill="#FF7378"
        ></path>
      </svg>
    )}
    style={{ fontSize: '32px' }}
    {...props}
  />
)

/**
 * 第三
 */
export const ThirdIcon = (props: CustomIconComponentProps) => (
  <Icon
    component={() => (
      <svg
        className="icon"
        viewBox="0 0 1024 1024"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
      >
        <path
          d="M218.4 611.2l57.6 57.6c6.4 6.4 10.4 16 10.4 25.6v244.8c0 13.6 14.4 22.4 26.4 15.2L493.6 852c11.2-7.2 26.4-7.2 37.6 0l180 100.8c12 7.2 27.2-0.8 27.2-15.2V694.4c0-9.6 4-19.2 10.4-25.6l57.6-57.6c6.4-6.4 10.4-16 10.4-25.6v-27.2c0-20-16-36-36-36H244c-20 0-36 16-36 36v27.2c0 9.6 3.2 18.4 10.4 25.6z"
          fill="#FAB10D"
        ></path>
        <path
          d="M488.8 736L237.6 601.6c-18.4-9.6-30.4-28.8-30.4-49.6V291.2c0-20.8 11.2-40 30.4-49.6l251.2-134.4c14.4-8 32.8-8 47.2 0l250.4 134.4c18.4 9.6 30.4 28.8 30.4 49.6v259.2c0 20.8-11.2 40-30.4 49.6L535.2 734.4c-14.4 9.6-32 9.6-46.4 1.6z"
          fill="#FFCE80"
        ></path>
        <path
          d="M479.2 682.4L276.8 572.8c-11.2-5.6-17.6-17.6-17.6-29.6V309.6c0-12 7.2-23.2 17.6-29.6l202.4-109.6c20.8-11.2 44.8-11.2 65.6 0L747.2 280c11.2 5.6 17.6 17.6 17.6 29.6V544c0 12-7.2 23.2-17.6 29.6L544.8 683.2c-20 10.4-44.8 10.4-65.6-0.8z"
          fill="#FFE8C2"
        ></path>
        <path
          d="M590.4 287.2c18.4 13.6 28 32 28 55.2 0 30.4-16.8 50.4-51.2 60 18.4 4.8 32 12.8 41.6 22.4 10.4 10.4 16 23.2 16 40 0 25.6-9.6 46.4-29.6 62.4-21.6 16-48 24.8-81.6 24.8-32 0-57.6-7.2-76.8-21.6-21.6-16-34.4-39.2-36.8-70.4h48c0.8 18.4 8 32.8 20.8 42.4C480 512 495.2 516 513.6 516c20 0 36-4.8 48-15.2 10.4-9.6 16-20.8 16-34.4 0-16-5.6-28-16.8-36-10.4-8-26.4-11.2-46.4-11.2H492V388h22.4c19.2 0 33.6-4 43.2-11.2 9.6-7.2 15.2-18.4 15.2-32 0-13.6-4.8-24-13.6-31.2-9.6-7.2-23.2-11.2-42.4-11.2s-33.6 4-44 12.8c-10.4 8.8-16.8 21.6-18.4 38.4H408c2.4-27.2 14.4-48 34.4-64 19.2-15.2 44-22.4 74.4-22.4 29.6 0 55.2 6.4 73.6 20z"
          fill="#FAB10D"
        ></path>
      </svg>
    )}
    style={{ fontSize: '32px' }}
    {...props}
  />
)
