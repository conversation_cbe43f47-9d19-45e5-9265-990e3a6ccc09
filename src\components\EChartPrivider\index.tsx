import React, { forwardRef, useRef } from 'react'
import 'echarts-wordcloud'
import EChartsReact, { EChartsReactProps } from 'echarts-for-react'
import { Spin } from 'antd'
import useResizeEcharts from '@/hooks/useResizeEcharts'
import styles from './index.module.less'
type EChartPrividerProps = {
  chartLoading?: boolean
  data: any
  wrapperStyle?: React.CSSProperties
  wrappClassName?: string
  children?: React.ReactNode
} & EChartsReactProps
const EChartPrivider = (
  {
    data,
    wrapperStyle,
    wrappClassName,
    children,
    chartLoading = true,
    ...EProps
  }: EChartPrividerProps,
  ref: any
) => {
  const containerRef = useRef(null) // 容器的引用
  const chartRef = ref || useRef<EChartsReact>(null)

  useResizeEcharts(chartRef, containerRef)
  return (
    <div
      ref={containerRef}
      className={wrappClassName}
      style={{ width: '100%', height: '100%', ...wrapperStyle }}
    >
      {chartLoading ? (
        <div className={styles.spinWrap}>
          <Spin size="default" className={styles.spin} />
        </div>
      ) : (
        data && (
          <EChartsReact
            ref={chartRef}
            opts={{ renderer: 'canvas' }}
            notMerge
            lazyUpdate
            style={{ width: '100%', height: '100%' }}
            {...EProps}
          />
        )
      )}
    </div>
  )
}

export default forwardRef<any, EChartPrividerProps>(EChartPrivider)
