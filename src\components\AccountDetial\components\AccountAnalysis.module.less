// 账号拆解AI内容
.analysisContent {
  padding: 12px 24px;
  .generateReport {
    display: flex;
    align-items: flex-start;
    justify-content: center;
    height: 100%;
    .reportTitle {
      position: relative;
      &::before {
        content: '';
        left: -12px;
        top: 50%;
        transform: translateY(-50%);
        position: absolute;
        width: 3px;
        height: 3px;
        border-radius: 50%;
        background: #1677ff;
      }
    }
  }

  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
  }

  .analysisItem {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .analysisTitle {
    display: flex;
    align-items: center;
  }

  .analysisNumber {
    font-size: 16px;
    font-weight: 600;
    color: #1677ff;
    margin-right: 8px;
  }

  .analysisLabel {
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  // 自定义 Markdown 内容样式
  .markdownContent {
    padding: 0 !important;
    margin: 0 !important;
    max-width: none !important;
    min-width: auto !important;
  }

  .videoExamples {
    margin-top: 20px;
  }

  // 诊断部分样式
  .diagnosisSection {
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;

    .ant-typography {
      &:first-child {
        min-width: 80px;
        flex-shrink: 0;
      }
    }
  }

  // 爆款解析样式
  .explosiveCase {
    background: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;

    .caseTitle {
      font-weight: 600;
      font-size: 14px;
      color: #262626;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    .caseItem {
      margin-bottom: 8px;
      display: flex;
      align-items: flex-start;

      .ant-typography {
        &:first-child {
          min-width: 80px;
          flex-shrink: 0;
          margin-right: 8px;
        }
      }
    }
  }

  // 脚本模版 Tabs 样式
  .scriptTemplatesTabs {
    .scriptTemplatesTabsBar {
      padding: 0 24px;
    }
  }

  // 商业变现样式
  .monetizationSection {
    .monetizationItem {
      margin-bottom: 16px;
      padding: 12px;
      background: #fff;
      border: 1px solid #e8e8e8;
      border-radius: 6px;

      .ant-typography {
        &:first-child {
          margin-right: 8px;
        }
      }
    }
  }

  .videoCard {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;

    .videoThumbnail {
      width: 100%;
      height: 120px;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .videoInfo {
      padding: 8px;

      .ant-typography {
        display: block;
        margin-bottom: 4px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.contentSection {
  border-radius: 12px;
  padding: 12px;
  background-color: #f8f9fb;
  margin-bottom: 12px;
  .workCard {
    cursor: pointer;
    padding: 12px;
    width: 33.33%;
    border-radius: 12px;
    background-color: #fff;
  }
}
