import React, { useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'antd'
import { LeftOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import { workDetailStore } from '@/store'
import BasicInfo from './components/BasicInfo'
import AIExtraction from './components/AIExtraction'
import LeftCard from './components/LeftCard'

import styles from './index.module.less'

const { Title } = Typography

interface WorkDetailProps {
  open: boolean
  onClose: () => void
  workId: string
  atUniqueId: string
}

const WorkDetail: React.FC<WorkDetailProps> = ({ open, onClose, workId, atUniqueId }) => {
  const handleTabChange = (key: string) => {
    workDetailStore.setActiveTab(key)
  }

  const handleClose = () => {
    workDetailStore.reset()
    onClose()
  }

  useEffect(() => {
    if (open && workId) {
      workDetailStore.setWrorkParams({ workId, atUniqueId })
      workDetailStore.fetchWorkDetail()
    }
    return () => {
      workDetailStore.reset()
    }
  }, [open, workId, workDetailStore])

  const tabItems = [
    {
      key: 'basic',
      label: '基本信息',
      children: <BasicInfo />
    },
    {
      key: 'ai',
      label: 'AI提取',
      destroyInactiveTabPane: true,
      children: <AIExtraction />
    }
  ]

  return (
    <Drawer
      loading={workDetailStore.loading}
      title={null}
      placement="left"
      onClose={handleClose}
      open={open}
      closable={false}
      width="100%"
      styles={{
        body: {
          padding: 0,
          height: '100%',
          background:
            'linear-gradient(to top, rgb(50, 102, 255), rgb(185, 203, 255), rgb(244, 245, 255))'
        }
      }}
      destroyOnClose
    >
      <div className={styles.workDetail}>
        {/* 顶部导航 */}
        <div className={styles.topNav}>
          <Button
            type="text"
            icon={<LeftOutlined />}
            onClick={handleClose}
            className={styles.backButton}
          >
            返回
          </Button>
          <Title level={4} className={styles.pageTitle} style={{ marginBottom: 0 }}>
            作品详情
          </Title>
          <div className={styles.navActions}>
            {/* <Button type="link" className={styles.navLink}>
              保存至库
            </Button> */}
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className={styles.mainContent}>
          {/* 左侧作品卡片 */}
          <LeftCard />

          {/* 右侧标签页区域 */}
          <div className={styles.tabsContainer}>
            <Tabs
              activeKey={workDetailStore.activeTab}
              onChange={handleTabChange}
              className={styles.workTabs}
              items={tabItems}
            />
          </div>
        </div>
      </div>
    </Drawer>
  )
}

export default observer(WorkDetail)
