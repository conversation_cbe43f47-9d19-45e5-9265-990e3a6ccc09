import { authStore } from '@/store'
import axios from 'axios'
import { authInfoStorage } from './storage'
import { message } from '@/components/GlobalTips'

export interface ResponseResult<T = any> {
  result: T
  code: string
  msg: string
}

const TIMEOUT = 30000
const baseUrl = import.meta.env.VITE_APP_URL // 接口地址

export const instance = axios.create({
  baseURL: baseUrl,
  timeout: TIMEOUT
})

instance.interceptors.request.use(
  config => {
    if (authStore.currentUserInfo?.token) {
      config.headers!.Authorization = `${authStore.currentUserInfo?.token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

instance.interceptors.response.use(
  response => {
    const { data, config } = response
    const { efficient } = config
    if (data.code === 'login_time_out') {
      message.error('账号信息过期,请重新登录')
      authInfoStorage.remove()
      return Promise.reject(data)
    }
    if (efficient && data.code !== 'success') {
      message.error(data.msg || '请求失败')
      return Promise.reject(data)
    }
    return Promise.resolve(data)
  },
  e => {
    message.error(e?.message || '请求失败')
    return Promise.reject(e)
  }
)

export default instance
