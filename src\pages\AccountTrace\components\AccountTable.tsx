import React, { useEffect, useRef, useState } from 'react'
import { Table, Avatar, Button, Space, Tag, Tooltip, Flex } from 'antd'
import type { TableColumnProps } from 'antd'
import { LineChartOutlined, TikTokFilled } from '@ant-design/icons'
import { AccountFilterBarValue } from './AccountFilterBar'
import AddAccountModal from './AddAccountModal'
import { AccountDetail } from '@/components'
import { message, modal } from '@/components/GlobalTips'
import { getAccountTraceList, deleteAccountTrace } from '@/services/accountTrace'
import { formatTimestamp, formatNumber } from '@/utils/delNumber'

const platForm: Record<string, any> = {
  tiktok: {
    name: 'tiktok',
    icon: <TikTokFilled />
  },
  douyin: {
    name: '抖音',
    icon: <TikTokFilled />
  }
}

interface AccountTableProps {
  filter: AccountFilterBarValue
}

const AccountTable: React.FC<AccountTableProps> = ({ filter }) => {
  const params = useRef({
    pageNo: 1,
    pageSize: 10
  })
  const [accountLimit, setAccountLimit] = useState({
    monitored: 3,
    remain: 7
  })
  const [data, setData] = useState<{
    data: any[]
    totalCount: number
  }>({
    data: [],
    totalCount: 0
  })
  const [loading, setLoading] = useState(true)
  const [modalOpen, setModalOpen] = useState(false)
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false)
  const [currentAccountId, setCurrentAccountId] = useState<string>()

  const handleAddAccount = () => {
    setModalOpen(false)
    getServerData()
    setAccountLimit(prev => ({ ...prev, remain: prev.remain - 1, monitored: prev.monitored + 1 }))
  }

  const handleViewDetail = (accountId: string) => {
    setCurrentAccountId(accountId)
    setDetailDrawerOpen(true)
  }

  const handleCloseDetail = () => {
    setDetailDrawerOpen(false)
    setCurrentAccountId(undefined)
  }

  // 删除监控账号
  const handleDelete = (name: string, atUniqueId: string) => {
    modal.confirm({
      title: '移除监控',
      content: `确定要移除"${name}"吗？移除后将无法恢复。`,
      okText: '确定',
      cancelText: '取消',
      okButtonProps: {
        danger: true
      },
      closable: true,
      onOk: () => {
        return deleteAccountTrace({ atUniqueId })
          .then(res => {
            if (res.code === 'success') {
              message.success('删除成功')
              getServerData({ pageNo: 1 })
            } else {
              message.error(res.msg || '删除失败')
            }
          })
          .catch(() => {
            message.error('删除失败')
          })
      }
    })
  }

  const onDelete = () => {
    handleCloseDetail()
    getServerData({ pageNo: 1 })
  }

  // 获取监控账号数据
  const getServerData = (_params?: any) => {
    params.current = {
      ...params.current,
      ..._params
    }
    setLoading(true)
    getAccountTraceList(params.current)
      .then(res => {
        if (res.code === 'success' && res.result.data) {
          setData({
            data: res.result.data.map((item: any) => {
              const { accountInfo, today, yesterday } = item
              return {
                key: accountInfo.atUniqueId || '1',
                atUniqueId: accountInfo.atUniqueId,
                avatar: accountInfo.avatar || '',
                name: accountInfo.name || '-',
                platform: accountInfo.platform,
                isSystem: false,
                fans: formatNumber(accountInfo.fansCount),
                works: formatNumber(accountInfo.resourceCount),
                region: accountInfo.registryLocation || '-',
                todayFans: formatNumber(today.addFansCount),
                yesterdayFans: formatNumber(yesterday.addFansCount),
                todayPlay: formatNumber(today.addPlayCount),
                yesterdayPlay: formatNumber(yesterday.addPlayCount),
                todayLike: formatNumber(today.addLikeCount),
                yesterdayLike: formatNumber(yesterday.addLikeCount),
                todayComment: formatNumber(today.addCommentCount) || 0,
                yesterdayComment: formatNumber(yesterday.addCommentCount),
                todayShare: formatNumber(today.addShareCount),
                yesterdayShare: formatNumber(yesterday.addShareCount),
                updateTime: formatTimestamp(accountInfo.lastSyncTime),
                addTime: formatTimestamp(accountInfo.createTime)
              }
            }),
            totalCount: res.result.totalCount
          })
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const getColumns = (): TableColumnProps[] => [
    {
      title: '账号信息',
      dataIndex: 'name',
      key: 'name',
      render: (_: any, record: any) => (
        <div style={{ display: 'flex', alignItems: 'flex-start' }}>
          <Avatar src={record.avatar} size={44} style={{ marginRight: 12 }} />
          <div style={{ minWidth: 0 }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 2 }}>
              <span
                style={{ fontWeight: 500, fontSize: 16, marginRight: 6, cursor: 'pointer' }}
                onClick={() => handleViewDetail(record.atUniqueId)}
              >
                {record.name}
              </span>
              {/* 平台icon可根据record.platform渲染 */}
              <span style={{ fontSize: 16, color: '#111', marginRight: 4 }}>
                {platForm?.[record.platform]?.icon}
              </span>
              {record.isSystem && (
                <Tag color="blue" style={{ marginLeft: 0 }}>
                  系统示例
                </Tag>
              )}
            </div>
            <div style={{ color: '#888', fontSize: 13, marginBottom: 2 }}>
              粉丝数：{record.fans} <span style={{ margin: '0 8px' }}>|</span> 作品数：
              {record.works}
            </div>
            <div style={{ color: '#888', fontSize: 13 }}>地区：{record.region}</div>
          </div>
        </div>
      )
    },
    {
      title: (
        <Flex align="center" justify="center">
          粉丝
          <Tooltip title="账号粉丝数，仅统计账号添加监控后的数据">
            <LineChartOutlined
              style={{ color: '#bbb', marginLeft: 4, fontSize: 16, verticalAlign: 'middle' }}
            />
          </Tooltip>
        </Flex>
      ),
      width: 140,
      dataIndex: 'todayFans',
      key: 'todayFans',
      align: 'center' as const,
      render: (val: any, record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>{val}</div>
          <div style={{ color: '#aaa', fontSize: 12 }}>昨日：{record.yesterdayFans}</div>
        </div>
      )
    },
    {
      title: (
        <Flex align="center" justify="center">
          播放
          <Tooltip title="账号播放数，仅统计账号添加监控后的数据">
            <LineChartOutlined
              style={{ color: '#bbb', marginLeft: 4, fontSize: 16, verticalAlign: 'middle' }}
            />
          </Tooltip>
        </Flex>
      ),
      width: 140,
      dataIndex: 'todayPlay',
      key: 'todayPlay',
      align: 'center' as const,
      render: (val: any, record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>{val}</div>
          <div style={{ color: '#aaa', fontSize: 12 }}>昨日新增：{record.yesterdayPlay}</div>
        </div>
      )
    },
    {
      title: (
        <Flex align="center" justify="center">
          点赞
          <Tooltip title="账号点赞数，仅统计账号添加监控后的数据">
            <LineChartOutlined
              style={{ color: '#bbb', marginLeft: 4, fontSize: 16, verticalAlign: 'middle' }}
            />
          </Tooltip>
        </Flex>
      ),
      width: 140,
      dataIndex: 'todayLike',
      key: 'todayLike',
      align: 'center' as const,
      render: (val: any, record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>{val}</div>
          <div style={{ color: '#aaa', fontSize: 12 }}>昨日：{record.yesterdayLike}</div>
        </div>
      )
    },
    {
      title: (
        <Flex align="center" justify="center">
          评论
          <Tooltip title="账号评论数，仅统计账号添加监控后的数据">
            <LineChartOutlined
              style={{ color: '#bbb', marginLeft: 4, fontSize: 16, verticalAlign: 'middle' }}
            />
          </Tooltip>
        </Flex>
      ),
      width: 140,
      dataIndex: 'todayComment',
      key: 'todayComment',
      align: 'center' as const,
      render: (val: any, record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>{val}</div>
          <div style={{ color: '#aaa', fontSize: 12 }}>昨日：{record.yesterdayComment}</div>
        </div>
      )
    },
    {
      title: (
        <Flex align="center" justify="center">
          转发
          <Tooltip title="账号转发数，仅统计账号添加监控后的数据">
            <LineChartOutlined
              style={{ color: '#bbb', marginLeft: 4, fontSize: 16, verticalAlign: 'middle' }}
            />
          </Tooltip>
        </Flex>
      ),
      width: 160,
      dataIndex: 'todayShare',
      key: 'todayShare',
      align: 'center' as const,
      render: (val: any, record: any) => (
        <div>
          <div style={{ fontWeight: 500 }}>{val}</div>
          <div style={{ color: '#aaa', fontSize: 12 }}>昨日：{record.yesterdayShare}</div>
        </div>
      )
    },
    {
      title: '最近更新时间',
      width: 160,
      dataIndex: 'updateTime',
      key: 'updateTime',
      align: 'center' as const
    },
    {
      title: '添加监控时间',
      width: 160,
      dataIndex: 'addTime',
      key: 'addTime',
      align: 'center' as const
    },
    {
      title: '操作',
      width: 140,
      fixed: 'right',
      key: 'action',
      align: 'center' as const,
      render: (_: any, record: any) => (
        <Space>
          <Button
            type="link"
            style={{ color: '#1677ff', padding: 0 }}
            onClick={() => handleViewDetail(record.key)}
          >
            详情
          </Button>
          <Button
            type="link"
            danger
            style={{ padding: 0 }}
            onClick={() => handleDelete(record.name, record.atUniqueId)}
          >
            移除
          </Button>
        </Space>
      )
    }
  ]

  useEffect(() => {
    getServerData(filter)
  }, [filter])

  return (
    <div style={{ width: '100%', borderRadius: 8, padding: 12 }}>
      {/* 统计与操作栏 */}
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '0 16px 12px 16px'
        }}
      >
        <div style={{ fontSize: 15 }}>
          {/* 共 <span style={{ color: '#1677ff', fontWeight: 500 }}>{data.total}</span> 条记录
          <span style={{ margin: '0 12px' }}>|</span> */}
          已监控账号：
          <span style={{ color: '#1677ff', fontWeight: 500 }}>{data.totalCount}</span>
          {/* <span style={{ margin: '0 12px' }}>|</span>
          剩余可监控：
          <span style={{ color: '#1677ff', fontWeight: 500 }}>{accountLimit.remain}</span> */}
        </div>
        <Button
          type="primary"
          disabled={accountLimit.remain === 0}
          onClick={() => setModalOpen(true)}
        >
          添加账号
        </Button>
      </div>
      <Table
        loading={loading}
        scroll={{ x: 'max-content' }}
        columns={getColumns()}
        dataSource={data.data}
        pagination={{
          total: data.totalCount,
          current: params.current.pageNo,
          pageSize: params.current.pageSize,
          onChange: (pageNo, pageSize) => {
            getServerData({ pageNo, pageSize })
          },
          position: ['bottomCenter'],
          showSizeChanger: false,
          showQuickJumper: true
        }}
        bordered={false}
      />
      {modalOpen && (
        <AddAccountModal
          open={modalOpen}
          onCancel={() => setModalOpen(false)}
          onOk={handleAddAccount}
        />
      )}
      {detailDrawerOpen && (
        <AccountDetail
          onDelete={onDelete}
          open={detailDrawerOpen}
          onClose={handleCloseDetail}
          atUniqueId={currentAccountId!}
        />
      )}
    </div>
  )
}

export default AccountTable
