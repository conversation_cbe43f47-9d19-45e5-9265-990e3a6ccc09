import { formatNumber } from '@/utils/delNumber'
import dayjs from 'dayjs'
import { type EChartsOption } from 'echarts'

// 通用类型定义
interface ChartDataItem {
  dateTime: string
  count: number
  [key: string]: any
}

interface ChartData {
  chartData?: {
    data: number[]
    dataType?: string[]
    dateType?: string[]
  }
  platform?: string[]
  work?: number[]
  like?: number[]
  comment?: number[]
  share?: number[]
}

// 通用配置
const commonGridConfig = {
  containLabel: true,
  left: 50,
  right: 30,
  bottom: 20,
  top: 40
}

const commonTooltipConfig = {
  trigger: 'axis' as const,
  confine: true,
  axisPointer: {
    type: 'shadow' as const
  }
}

const commonBarConfig = {
  type: 'bar' as const,
  barWidth: '40%',
  label: {
    show: true,
    position: 'top' as const
  }
}

// 发布频率图表配置
const getPublishFrequencyOption = ({
  dateTime,
  releaseCount
}: {
  dateTime: string[]
  releaseCount: number[]
}): EChartsOption => ({
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(50, 50, 50, 0.9)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff'
    }
  },
  xAxis: {
    type: 'category',
    data: dateTime,
    axisLine: {
      lineStyle: {
        color: '#e8e8e8'
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#666',
      fontSize: 12,
      formatter: (value: string) => {
        return dayjs(value).format('MM/DD')
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#666',
      fontSize: 12,
      formatter: (value: number) => {
        return formatNumber(value)
      }
    },
    splitLine: {
      lineStyle: {
        color: '#f0f0f0'
      }
    }
  },
  series: [
    {
      name: '发布作品',
      data: releaseCount,
      type: 'bar',
      itemStyle: {
        color: '#1677ff',
        borderRadius: [4, 4, 0, 0]
      },
      barWidth: '60%'
    }
  ]
})

export { getPublishFrequencyOption }
