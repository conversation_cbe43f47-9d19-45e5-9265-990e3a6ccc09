.basicInfoContent {
  padding: 12px 24px;
  height: 100%;
  overflow-y: auto;
}

.commentSection {
  margin-top: 16px;
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.sectionTitleWrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.titleIndicator {
  width: 4px;
  height: 16px;
  background: #1677ff;
  border-radius: 2px;
}

.sectionTitle {
  margin: 0 !important;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.sectionActions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.updateTime {
  font-size: 12px;
  color: #8c8c8c;
}

// 素材概览
.titleSection {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.workTitle {
  font-size: 14px;
  color: #262626;
  margin: 0 !important;
  line-height: 1.5;
}

.infoGrid {
  margin-bottom: 24px;
}

.infoRow {
  display: flex;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.infoItem {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;

  &:not(:last-child) {
    margin-right: 12px;
  }
}

.updateTimeRow {
  display: flex;
  align-items: center;
  gap: 8px;
}

.infoLabel {
  font-size: 13px;
  white-space: nowrap;
  color: #8c8c8c;
  font-weight: 500;
}

.infoValue {
  font-size: 14px;
  color: #262626;
}

.tagsContainer {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.topicTag {
  font-size: 12px;
  border-radius: 4px;
  margin: 0;
}

.moreTopics {
  padding: 0;
  height: auto;
  font-size: 12px;
  color: #1677ff;
}

.linkButton {
  padding: 0;
  height: auto;
  font-size: 14px;
  text-align: left;
  justify-content: flex-start;

  &:hover {
    text-decoration: underline;
  }
}

.updateButton {
  padding: 0;
  height: auto;
  margin-left: 8px;
}

// 数据统计
.statsGrid {
  display: flex;
  gap: 16px;
  border-radius: 8px;
}

.statCard {
  flex: 1;
  padding: 16px;
  text-align: center;
  background: #f8f9fa;
}

.statLabel {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.statValue {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

// 评论内容
.sortControls {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.sortButton {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #8c8c8c;
  cursor: pointer;
  padding: 4px 0;
  transition: all 0.2s ease;

  &:hover {
    color: #1677ff;
  }

  &.active {
    color: #1677ff;
  }

  :global(.anticon) {
    font-size: 12px;
  }
}

.commentsContainer {
}

.commentItem {
  border: none !important;
  padding: 12px 0 !important;
  border-bottom: 1px solid #f0f0f0 !important;

  &:last-child {
    border-bottom: none !important;
  }
}

.commentContent {
  width: 100%;
}

.commentHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.commentAuthor {
  display: flex;
  align-items: center;
  gap: 8px;
}

.authorName {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
}

.commentTime {
  font-size: 12px;
  color: #8c8c8c;
}

.commentLikes {
  display: flex;
  align-items: center;
  gap: 4px;
}

.likeIcon {
  font-size: 12px;
  color: #8c8c8c;
}

.likeCount {
  font-size: 12px;
  color: #8c8c8c;
}

.commentText {
  font-size: 14px;
  color: #262626;
  line-height: 1.5;
}
