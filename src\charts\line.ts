import dayjs from 'dayjs'
import { formatNumber } from '@/utils/delNumber'
import type { EChartsOption } from 'echarts'

// 粉丝趋势图表配置
const getFollowerTrendOption = ({
  label,
  dateTime,
  statItem
}: {
  label: string
  dateTime: string[]
  statItem: number[]
}): EChartsOption => ({
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(50, 50, 50, 0.9)',
    borderColor: 'transparent',
    textStyle: {
      color: '#fff'
    }
  },
  xAxis: {
    type: 'category',
    data: dateTime,
    axisLine: {
      lineStyle: {
        color: '#e8e8e8'
      }
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      color: '#999',
      fontSize: 12,
      formatter: (value: string) => {
        return dayjs(value).format('MM/DD')
      }
    }
  },
  yAxis: {
    type: 'value',
    axisLine: {
      show: false
    },
    axisTick: {
      show: false
    },
    axisLabel: {
      formatter: (value: number) => {
        return formatNumber(value)
      },
      color: '#999',
      fontSize: 12
    },
    splitLine: {
      lineStyle: {
        color: '#f0f0f0',
        type: 'dashed'
      }
    }
  },
  series: [
    {
      name: label,
      data: statItem,
      type: 'line',
      smooth: true,
      symbol: 'none',
      lineStyle: {
        color: '#5470c6',
        width: 3
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: 'rgba(84, 112, 198, 0.3)'
            },
            {
              offset: 1,
              color: 'rgba(84, 112, 198, 0.1)'
            }
          ]
        }
      }
    }
  ]
})

export { getFollowerTrendOption }
