.workDetail {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

// 顶部导航
.topNav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.backButton {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;

  &:hover {
    color: #1677ff;
  }
}

.pageTitle {
  margin: 0 0 0 16px;
  font-size: 18px;
  font-weight: 600;
}

.navActions {
  display: flex;
  align-items: center;
}

.navLink {
  padding: 0;
  font-size: 14px;
  color: #1677ff;
}

// 主要内容区域
.mainContent {
  display: flex;
  gap: 24px;
  padding: 0 24px;
  height: calc(100vh - 68px);
  overflow: hidden;
}

// 右侧标签页区域
.tabsContainer {
  flex: 1;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.workTabs {
  height: 100%;
  display: flex;
  flex-direction: column;

  :global(.ant-tabs-content-holder) {
    flex: 1;
    overflow: auto;
  }

  :global(.ant-tabs-content) {
    height: 100%;
  }

  :global(.ant-tabs-tabpane) {
    height: 100%;
    overflow: auto;
  }

  :global(.ant-tabs-nav) {
    margin: 0;
    padding: 0 24px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
  }

  :global(.ant-tabs-tab) {
    padding: 16px 0;
    margin-right: 32px;
    font-size: 16px;
    font-weight: 500;
  }
}
