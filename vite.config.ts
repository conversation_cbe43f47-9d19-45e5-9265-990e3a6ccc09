import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import eslintPlugin from 'vite-plugin-eslint'
// import { visualizer } from 'rollup-plugin-visualizer'
import viteCompression from 'vite-plugin-compression'
// import { viteMockServe } from 'vite-plugin-mock'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig(() => {
  return {
    plugins: [
      viteCompression({
        threshold: 10250,
        algorithm: 'gzip', // 或 'gzip'
        ext: '.gz' // 压缩文件的后缀
      }),
      react(),
      eslintPlugin({
        include: [
          'src/**/*.js',
          'src/**/*.ts',
          'src/**/*.tsx',
          'src/*.js',
          'src/*.ts',
          'src/*.tsx'
        ],
        failOnError: false // 遇到 ESLint 错误时不会中断
      })
      // mode === 'mock' &&
      //   viteMockServe({
      //     // default
      //     mockPath: './config/mock'
      //   })
    ],
    resolve: {
      extensions: ['.mjs', '.js', '.jsx', '.ts', '.tsx', '.json', '.sass', '.scss'], // 忽略输入的扩展名
      alias: [
        { find: /^~/, replacement: '' },
        { find: '@', replacement: path.resolve(__dirname, 'src') },
        { find: '~', replacement: path.resolve(__dirname, './node_modules') },
        {
          find: '@components',
          replacement: path.resolve(__dirname, 'src/components')
        },
        { find: '@config', replacement: path.resolve(__dirname, 'config') }
      ]
    },
    css: {
      preprocessorOptions: {
        less: {
          // 支持内联 JavaScript
          javascriptEnabled: true
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: 3003,
      hmr: {
        overlay: false // 控制是否在页面上显示 HMR 错误
      }
    },
    build: {
      sourcemap: false,
      minify: 'esbuild',
      rollupOptions: {
        output: {
          entryFileNames: 'assets/index-[hash].js',
          chunkFileNames: 'assets/chunk-[hash].js',
          assetFileNames: 'assets/[name].[ext]',
          manualChunks(id) {
            if (id.includes('echarts')) {
              return 'echarts'
            }
            if (id.includes('@ant-design/pro-components')) {
              return 'pro-components'
            }
            if (id.includes('antd')) {
              return 'antd'
            }
          }
        }
      }
    }
  }
})
