import { useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Avatar, Spin, Empty } from 'antd'
import { ReloadOutlined, CopyOutlined, HeartOutlined, LinkOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import styles from './BasicInfo.module.less'
import { workDetailStore } from '@/store'
import { formatNumber, formatTimestamp } from '@/utils/delNumber'
import ClipboardJS from 'clipboard'
import { message } from '@/components/GlobalTips'
import { getWorkCommentStatus } from '@/services/workDetail'

const { Title, Paragraph } = Typography

const CommentSection = observer(() => {
  const { commentInfo, workParams } = workDetailStore
  const { workId } = workParams!
  const pageNo = useRef(1)
  const [updateLoading, setUpdateLoading] = useState(false)
  const [loading, setLoading] = useState(false)
  const [interLoading, setInterLoading] = useState(true)
  const timer = useRef<number | null>(null)

  const clearTimer = () => {
    if (timer.current) {
      clearTimeout(timer.current)
      timer.current = null
    }
  }

  const onLoadMore = async () => {
    if (!workId) {
      return
    }

    setLoading(true)
    try {
      pageNo.current = pageNo.current + 1
      await workDetailStore.loadMoreComments(pageNo.current)
    } catch (error) {
      console.error('加载更多评论失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 刷新评论
  const handleRefreshComments = async () => {
    if (!workId) {
      return
    }
    setUpdateLoading(true)
    try {
      await workDetailStore.refreshComments()
      pageNo.current = 1 // 重置页码
      getCommentStatus()
    } catch (error) {
      console.error('刷新评论失败:', error)
    } finally {
      setUpdateLoading(false)
    }
  }

  const getCommentStatus = async () => {
    clearTimer()
    getWorkCommentStatus(workParams!)
      .then(async res => {
        if (res.code === 'success') {
          if (res.result) {
            setUpdateLoading(true)
            timer.current = window.setTimeout(() => getCommentStatus(), 2000)
          } else {
            pageNo.current = 1 // 重置页码
            workDetailStore.setCommentInfo({
              pageNo: 1,
              pageSize: 20,
              data: [],
              hasNextPage: true,
              totalCount: 0
            })
            await workDetailStore.loadMoreComments(1)
            setUpdateLoading(false)
            setInterLoading(false)
          }
        }
      })
      .finally(() => {
        setLoading(false)
      })
  }

  const loadMore = (
    <div
      style={{
        textAlign: 'center',
        marginTop: 12,
        height: 32,
        lineHeight: '32px'
      }}
    >
      {commentInfo.hasNextPage ? (
        <Button onClick={onLoadMore}>加载更多</Button>
      ) : (
        commentInfo.totalCount > 0 && <div>没有更多数据</div>
      )}
    </div>
  )

  useEffect(() => {
    getCommentStatus()
    return () => {
      clearTimer()
    }
  }, [])

  return (
    <div className={styles.commentSection}>
      <div className={styles.sectionHeader}>
        <div className={styles.sectionTitleWrapper}>
          <div className={styles.titleIndicator}></div>
          <Title level={5} className={styles.sectionTitle}>
            评论内容 ({commentInfo.totalCount})
          </Title>
        </div>
        <div className={styles.sectionActions}>
          {/* <span className={styles.updateTime}>
            最后更新时间：{formatTimestamp(workDetail?.workInfo.updateTime)}
          </span> */}
          <Button
            type="link"
            icon={<ReloadOutlined />}
            size="small"
            loading={updateLoading}
            onClick={handleRefreshComments}
          >
            更新
          </Button>
        </div>
      </div>

      {/* 评论列表 */}
      <div className={styles.commentsContainer}>
        {commentInfo.data.length > 0 ? (
          <List
            loadMore={!loading && loadMore}
            dataSource={commentInfo.data}
            renderItem={comment => (
              <List.Item className={styles.commentItem}>
                <div className={styles.commentContent}>
                  <div className={styles.commentHeader}>
                    <div className={styles.commentAuthor}>
                      <Avatar size={24} src={comment.commenterAvatar} />
                      <span className={styles.authorName}>{comment.commenterName}</span>
                      <span className={styles.commentTime}>
                        {formatTimestamp(comment.publishTime)}
                      </span>
                    </div>
                    <div className={styles.commentLikes}>
                      <HeartOutlined className={styles.likeIcon} />
                      <span className={styles.likeCount}>{comment.likeCount}</span>
                    </div>
                  </div>
                  {comment.content && <div className={styles.commentText}>{comment.content}</div>}
                </div>
              </List.Item>
            )}
          />
        ) : (
          <Empty description="暂无评论" />
        )}
        {loading && (
          <Spin
            style={{
              textAlign: 'center'
            }}
            tip="加载中..."
          >
            <span></span>
          </Spin>
        )}
      </div>
    </div>
  )
})

const WorkInfoSection = observer(() => {
  const [loading, setLoading] = useState(false)
  const { workDetail } = workDetailStore

  const handleRefresh = async () => {
    setLoading(true)
    workDetailStore.refreshWorkInfo().finally(() => {
      setLoading(false)
    })
  }

  if (!workDetail) {
    return null
  }

  return (
    <div className={styles.section}>
      <div className={styles.sectionHeader}>
        <div className={styles.sectionTitleWrapper}>
          <div className={styles.titleIndicator}></div>
          <Title level={5} className={styles.sectionTitle}>
            作品概览
          </Title>
        </div>
      </div>

      {/* 标题 */}
      <div className={styles.titleSection}>
        <Paragraph className={styles.workTitle} ellipsis={{ rows: 1, tooltip: true }}>
          {workDetail.workInfo.title}
        </Paragraph>
        <div className={styles.sectionActions}>
          <Button
            type="text"
            icon={<CopyOutlined />}
            size="small"
            onClick={() => {
              ClipboardJS.copy(workDetail.workInfo.title)
              message.success('复制成功')
            }}
          />
        </div>
      </div>

      {/* 基本信息网格 */}
      <div className={styles.infoGrid}>
        <div className={styles.infoRow}>
          {/* <div className={styles.infoItem}>
            <span className={styles.infoLabel}>作品类型：</span>
            <span className={styles.infoValue}>自然流</span>
          </div> */}
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>参与话题：</span>
            <Paragraph
              style={{ flex: 1, width: 200, marginBottom: 0 }}
              ellipsis={{ rows: 1, tooltip: true }}
            >
              {workDetail.workInfo.hashtags
                ? '#' + workDetail.workInfo.hashtags.split(';').join(' #')
                : '-'}
            </Paragraph>
          </div>
          {/* <div className={styles.infoItem}>
            <span className={styles.infoLabel}>带货链接：</span>
            <Button
              type="link"
              icon={<LinkOutlined />}
              className={styles.linkButton}
              href={workDetail.workInfo.url}
              target="_blank"
            >
              点击查看
            </Button>
          </div> */}
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>作品来源链接：</span>
            <Button
              type="link"
              icon={<LinkOutlined />}
              className={styles.linkButton}
              href={workDetail.workInfo.url}
              target="_blank"
            >
              点击查看
            </Button>
          </div>
        </div>

        <div className={styles.infoRow}>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>发布时间：</span>
            <Paragraph style={{ marginBottom: 0 }} ellipsis className={styles.infoValue}>
              {formatTimestamp(workDetail.workInfo.publishTime)}
            </Paragraph>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.infoLabel}>最后更新时间：</span>
            <div className={styles.updateTimeRow}>
              <Paragraph style={{ marginBottom: 0 }} ellipsis className={styles.infoValue}>
                {formatTimestamp(workDetail.workInfo.updateTime)}
              </Paragraph>
              <Button
                loading={loading}
                onClick={handleRefresh}
                type="link"
                icon={<ReloadOutlined />}
                size="small"
                className={styles.updateButton}
              >
                更新
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* 数据统计 */}
      <div className={styles.statsGrid}>
        <div className={styles.statCard}>
          <div className={styles.statLabel}>播放量</div>
          <div className={styles.statValue}>{formatNumber(workDetail.workInfo.playCount)}</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statLabel}>点赞数</div>
          <div className={styles.statValue}>{formatNumber(workDetail.workInfo.likeCount)}</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statLabel}>评论数</div>
          <div className={styles.statValue}>{workDetail.workInfo.commentCount}</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statLabel}>转发数</div>
          <div className={styles.statValue}>{formatNumber(workDetail.workInfo.shareCount)}</div>
        </div>
        <div className={styles.statCard}>
          <div className={styles.statLabel}>收藏数</div>
          <div className={styles.statValue}>{formatNumber(workDetail.workInfo.collectCount)}</div>
        </div>
      </div>
    </div>
  )
})

const BasicInfo = () => {
  const { workDetail } = workDetailStore

  if (!workDetail) {
    return null
  }
  return (
    <div className={styles.basicInfoContent}>
      {/* 作品概览 */}
      <WorkInfoSection />
      <CommentSection />
    </div>
  )
}

export default observer(BasicInfo)
