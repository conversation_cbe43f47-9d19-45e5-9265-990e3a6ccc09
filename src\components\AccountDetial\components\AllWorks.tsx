import { useState, useEffect, useCallback, useRef } from 'react'
import { message, Spin, Divider, Flex, Empty, Tag, Input, Checkbox } from 'antd'
import InfiniteScroll from 'react-infinite-scroll-component'
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons'
import { getAuthorWorkList, getTopicTagList } from '@/services/accountDetail'
import { accountDetailStore } from '@/store'
import WorkItemCard from '@/components/WorkItemCard'
import styles from './AllWorks.module.less'

const PAGE_SIZE = 40

const useDebounce = (value: string, delay = 300) => {
  const [debounced, setDebounced] = useState(value)
  useEffect(() => {
    const timer = setTimeout(() => setDebounced(value), delay)
    return () => clearTimeout(timer)
  }, [value, delay])
  return debounced
}

const AllWorks = () => {
  const { atUniqueId } = accountDetailStore
  const [page, setPage] = useState(1)
  const [loading, setLoading] = useState(false)
  const [works, setWorks] = useState<any[]>([])
  const [hasMore, setHasMore] = useState(true)
  const [availableTags, setAvailableTags] = useState<{ tag: string; count: number }[]>([])
  const [searchInput, setSearchInput] = useState('')
  const initializedRef = useRef(false)

  const debouncedKeyword = useDebounce(searchInput.trim(), 400)

  const [filter, setFilter] = useState({
    publishTime: '全部',
    selectedTag: '全部',
    keyword: '',
    onlyHotWorks: false,
    sortBy: '发布时间',
    sortOrder: 'desc'
  })

  // 同步关键词变化
  useEffect(() => {
    if (initializedRef.current) {
      setFilter(prev => ({ ...prev, keyword: debouncedKeyword }))
    } else {
      initializedRef.current = true
    }
  }, [debouncedKeyword])

  const buildApiParams = useCallback(
    (pageNo: number) => {
      const params: any = {
        hotVideo: 0,
        atUniqueId,
        pageNo,
        pageSize: PAGE_SIZE
      }

      if (filter.keyword?.trim()) {
        params.search = filter.keyword.trim()
      }
      if (filter.selectedTag && filter.selectedTag !== '全部') {
        params.topicTag = filter.selectedTag
      }

      if (filter.onlyHotWorks) {
        params.hotVideo = filter.onlyHotWorks ? 1 : 0
      }
      if (filter.publishTime !== '全部') {
        const now = new Date()
        let startTime = new Date()
        const daysMap = { 今日: 0, 近7日: 7, 近14日: 14, 近30日: 30 }
        const days = daysMap[filter.publishTime as keyof typeof daysMap]
        if (typeof days === 'number') {
          startTime = new Date(now.getTime() - days * 24 * 60 * 60 * 1000)
        }
        params.startTime = startTime.getTime()
        params.endTime = now.getTime()
      }

      if (filter.sortBy && filter.sortOrder !== 'none') {
        const sortFieldMap: Record<string, number> = {
          发布时间: 0,
          点赞数: 1,
          评论数: 2,
          转发数: 3
        }
        params.sortFields = sortFieldMap[filter.sortBy]
        params.sortOrder = filter.sortOrder === 'desc' ? 0 : 1
      }

      return params
    },
    [atUniqueId, filter]
  )

  const fetchWorksList = async (reset = false) => {
    if (loading) {
      return
    }
    const pageNo = reset ? 1 : page
    const params = buildApiParams(pageNo)

    setLoading(true)
    try {
      const res = await getAuthorWorkList(params)
      if (res.code === 'success') {
        const { data, totalCount, pageNo, pageSize } = res.result
        const apiHasMore = totalCount > pageNo * pageSize
        const newWorks = data || []

        setWorks(prev => (reset ? newWorks : [...prev, ...newWorks]))
        setPage(pageNo + 1)
        setHasMore(apiHasMore && newWorks.length > 0)
      } else {
        message.error(res.msg || '加载失败')
      }
    } catch (error) {
      console.error('加载失败:', error)
      message.error('网络连接失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (atUniqueId) {
      setWorks([])
      setHasMore(true)
      fetchWorksList(true)
    }
  }, [atUniqueId, filter])

  useEffect(() => {
    if (atUniqueId) {
      getTopicTagList({ atUniqueId })
        .then(res => {
          if (res.code === 'success' && res.result) {
            setAvailableTags(res.result.slice(0, 10))
          }
        })
        .catch(error => {
          console.error('获取标签失败:', error)
        })
    }
  }, [atUniqueId])

  return (
    <div className={styles.worksContent}>
      <FilterPanel
        filter={filter}
        setFilter={setFilter}
        availableTags={availableTags}
        searchInput={searchInput}
        setSearchInput={setSearchInput}
      />
      <SortSelector filter={filter} setFilter={setFilter} />

      {works.length === 0 && !loading ? (
        <div style={{ padding: '40px 12px', textAlign: 'center' }}>
          <Empty description="暂无作品数据" />
        </div>
      ) : (
        <InfiniteScroll
          dataLength={works.length}
          next={() => fetchWorksList(false)}
          hasMore={hasMore}
          loader={
            <Flex
              style={{ width: '100%', textAlign: 'center', padding: 16 }}
              align="center"
              justify="center"
            >
              <Spin size="small" />
              <span style={{ marginLeft: 8, color: '#888' }}>加载中...</span>
            </Flex>
          }
          endMessage={
            <div style={{ textAlign: 'center', padding: '16px', color: '#999' }}>
              <Divider plain>没有更多作品了 🤐</Divider>
            </div>
          }
          scrollableTarget="accountDetail"
        >
          <div className={styles.worksGrid}>
            {works.map(work => (
              <div key={work.workId} style={{ minWidth: 240 }}>
                <WorkItemCard itemData={{ ...work, atUniqueId }} type="detail" />
              </div>
            ))}
          </div>
        </InfiniteScroll>
      )}
    </div>
  )
}

// 子组件：FilterPanel
const FilterPanel = ({ filter, setFilter, availableTags, searchInput, setSearchInput }: any) => {
  const timeFilters = ['全部', '今日', '近7日', '近14日', '近30日']

  return (
    <div className={styles.filterSection}>
      <div className={styles.filterRow}>
        <div style={{ marginRight: 8, fontWeight: 500, width: 80 }}>发布时间：</div>
        <div className={styles.filterSelectLabel}>
          {timeFilters.map(time => (
            <Tag.CheckableTag
              key={time}
              className="selectTag"
              checked={filter.publishTime === time}
              onChange={() => setFilter((prev: any) => ({ ...prev, publishTime: time }))}
            >
              {time}
            </Tag.CheckableTag>
          ))}
        </div>
      </div>

      <div className={styles.filterRow}>
        <div style={{ marginRight: 8, fontWeight: 500, width: 80 }}>话题标签：</div>
        <div className={styles.filterSelectLabel}>
          <Tag.CheckableTag
            className="selectTag"
            checked={filter.selectedTag === '全部'}
            onChange={() => setFilter((prev: any) => ({ ...prev, selectedTag: '全部' }))}
          >
            全部
          </Tag.CheckableTag>
          {availableTags.map((item: any) => (
            <Tag.CheckableTag
              key={item.tag}
              className="selectTag"
              checked={filter.selectedTag === item.tag}
              onChange={() => setFilter((prev: any) => ({ ...prev, selectedTag: item.tag }))}
            >
              {item.tag}
            </Tag.CheckableTag>
          ))}
        </div>
      </div>

      <div className={styles.filterRow}>
        <Input
          placeholder="请输入作品标题"
          style={{ width: 300 }}
          value={searchInput}
          allowClear
          onChange={e => setSearchInput(e.target.value)}
        />
      </div>
    </div>
  )
}

// 子组件：SortSelector（保持不变）
const SortSelector = ({ filter, setFilter }: any) => {
  const options = ['发布时间', '点赞数', '评论数', '转发数']

  const toggleSort = (sortBy: string) => {
    setFilter((prev: any) => {
      if (prev.sortBy === sortBy) {
        if (prev.sortOrder === 'none') {
          return { ...prev, sortBy, sortOrder: 'desc' }
        }
        if (prev.sortOrder === 'desc') {
          return { ...prev, sortBy, sortOrder: 'asc' }
        }
        return { ...prev, sortBy: '', sortOrder: 'none' }
      }
      return { ...prev, sortBy, sortOrder: 'desc' }
    })
  }

  const getIcon = (sortBy: string) => {
    if (filter.sortBy === sortBy) {
      return filter.sortOrder === 'desc' ? <CaretDownOutlined /> : <CaretUpOutlined />
    }
    return <CaretDownOutlined style={{ opacity: 0.3 }} />
  }

  return (
    <Flex
      justify="space-between"
      style={{
        marginBottom: 16
      }}
    >
      <Checkbox
        checked={filter.onlyHotWorks}
        onChange={e => setFilter((prev: any) => ({ ...prev, onlyHotWorks: e.target.checked }))}
      >
        仅查看爆款作品
      </Checkbox>
      <div
        style={{
          fontSize: 14,
          display: 'flex',
          justifyContent: 'flex-end',
          gap: 16
        }}
      >
        {options.map(option => (
          <div
            key={option}
            style={{ cursor: 'pointer', color: filter.sortBy === option ? '#1677ff' : '#666' }}
            onClick={() => toggleSort(option)}
          >
            {option} {getIcon(option)}
          </div>
        ))}
      </div>
    </Flex>
  )
}

export default AllWorks
