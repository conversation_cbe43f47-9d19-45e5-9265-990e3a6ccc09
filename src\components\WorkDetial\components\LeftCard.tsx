import { useRef, useState, useEffect } from 'react'
import { Ava<PERSON>, Button, Card, Typography } from 'antd'
import { PlayCircleOutlined, UserOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import workDetailStore from '@/store/workDetailStore'
import styles from './LeftCard.module.less'
import { formatNumber } from '@/utils/delNumber'
import { daonload } from '@/utils/file/download'

const LeftCard = () => {
  const { workDetail } = workDetailStore
  const [playing, setPlaying] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)

  const handlePlay = () => {
    setPlaying(true)
    videoRef.current?.play()
    workDetailStore.setVideoPlaying(true)
  }

  const handlePause = () => {
    setPlaying(false)
    videoRef.current?.pause()
    workDetailStore.setVideoPlaying(false)
  }

  // 监听视频时间更新
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      workDetailStore.setCurrentVideoTime(videoRef.current.currentTime)
    }
  }

  // 监听视频加载完成，获取总时长
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      workDetailStore.setVideoDuration(videoRef.current.duration)
    }
  }

  // 跳转到指定时间
  const seekToTime = (time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time
      workDetailStore.setCurrentVideoTime(time)
    }
  }

  // 监听 store 中的视频时间变化，实现外部控制视频跳转
  useEffect(() => {
    // 将跳转方法暴露给 store，供其他组件调用
    workDetailStore.seekToTime = seekToTime
  }, [])

  useEffect(() => {
    const video = videoRef.current
    if (video) {
      const handlePlayEvent = () => {
        setPlaying(true)
        workDetailStore.setVideoPlaying(true)
      }

      const handlePauseEvent = () => {
        setPlaying(false)
        workDetailStore.setVideoPlaying(false)
      }

      video.addEventListener('timeupdate', handleTimeUpdate)
      video.addEventListener('loadedmetadata', handleLoadedMetadata)
      video.addEventListener('play', handlePlayEvent)
      video.addEventListener('pause', handlePauseEvent)

      return () => {
        video.removeEventListener('timeupdate', handleTimeUpdate)
        video.removeEventListener('loadedmetadata', handleLoadedMetadata)
        video.removeEventListener('play', handlePlayEvent)
        video.removeEventListener('pause', handlePauseEvent)
      }
    }
  }, [])

  if (!workDetail) {
    return null
  }
  return (
    <div className={styles.workCard}>
      <Card className={styles.videoCard} onMouseEnter={handlePlay} onMouseLeave={handlePause}>
        {/* 顶部账号信息 */}
        <div className={styles.accountHeader}>
          <Avatar size={48} src={workDetail.authorInfo.authorAvatar} icon={<UserOutlined />} />
          <div className={styles.accountInfo}>
            <Typography.Paragraph
              ellipsis={{ rows: 1, tooltip: true }}
              className={styles.accountName}
            >
              {workDetail.authorInfo.authorName}
            </Typography.Paragraph>
            <div className={styles.accountStats}>
              <span className={styles.statItem}>
                粉丝数：
                <span className={styles.statValue}>
                  {formatNumber(workDetail.authorInfo.followerCount)}
                </span>
              </span>
              <span className={styles.statItem}>
                作品数：
                <span className={styles.statValue}>
                  {formatNumber(workDetail.authorInfo.workCount)}
                </span>
              </span>
              <span className={styles.statItem}>
                点赞数：
                <span className={styles.statValue}>
                  {formatNumber(workDetail.authorInfo.likeCount)}
                </span>
              </span>
            </div>
          </div>
        </div>

        {/* 中间视频区域 */}
        <div className={styles.videoContainer}>
          <video
            ref={videoRef}
            width="100%"
            height={480}
            controlsList="nodownload"
            disablePictureInPicture
            poster={workDetail.workInfo.thumbnailLink}
            className={styles.video}
            controls={playing}
          >
            <source
              src={
                workDetail.workInfo.videoUrl ||
                'https://linknext-**********.cos.ap-hongkong.myqcloud.com/material/1/20250522/media/7505129228468325674.mp4'
              }
              type="video/mp4"
            />
            您的浏览器不支持 video 标签。
          </video>
          {!playing && <PlayCircleOutlined onClick={handlePlay} className={styles.playButton} />}
          {/* <div className={styles.videoDuration}>{workDetail?.duration}</div> */}
        </div>

        {/* 底部下载区域 */}
        <div className={styles.downloadSection}>
          <Button
            type="text"
            block
            size="large"
            className={styles.downloadButton}
            onClick={() => {
              daonload({
                url: workDetail.workInfo.videoUrl,
                name: workDetail.workInfo.title
              })
            }}
          >
            视频下载
          </Button>
        </div>
      </Card>
    </div>
  )
}

export default observer(LeftCard)
