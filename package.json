{"name": "vite-react-ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite", "start:mock": "vite --mode mock", "start:production": "vite --mode production", "build": "vite build --mode production", "build:test": "vite build --mode test", "preview": "vite preview", "lint:script": "eslint --ext .js,.jsx,.ts,.tsx --fix  ./", "lint:style": "stylelint --fix  **/*.{css,less,scss}"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write", "git add ."], "*.{json.md,xml,svg,html,js,jsx}": "prettier --write", "*.less": ["stylelint --fix  --custom-syntax postcss-less", "git add ."]}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/pro-components": "^2.8.6", "ahooks": "^3.7.5", "antd": "5.24.1", "axios": "^1.3.4", "classnames": "^2.5.1", "clipboard": "^2.0.11", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "echarts-wordcloud": "^2.1.0", "js-base64": "^3.7.7", "less": "^4.1.3", "lodash-es": "^4.17.21", "mobx": "^6.8.0", "mobx-react-lite": "^4.1.0", "nprogress": "^0.2.0", "postcss": "^8.4.21", "postcss-less": "^6.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-lazyload": "^3.2.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.28.0", "react-sticky-box": "^2.0.5", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/eslint-parser": "^7.19.1", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^18.14.5", "@types/nprogress": "^0.2.3", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@types/react-lazyload": "^3.2.3", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.56.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "husky": "^8.0.3", "lint-staged": "^13.1.2", "mockjs": "^1.1.0", "prettier": "^2.8.4", "rollup-plugin-visualizer": "^5.12.0", "stylelint": "^15.2.0", "stylelint-config-standard": "^30.0.1", "typescript": "5.3.2", "vite": "^5.1.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1"}}