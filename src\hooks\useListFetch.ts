import { useEffect, useState, useCallback } from 'react'

interface ResponseType<T> {
  code: string
  result: T
  message?: string
}

interface FetchType<T, P> {
  fetchFunction: (params: P) => Promise<ResponseType<T>>
  params: P
  immediate?: boolean
  defaultLoading?: boolean
}

const useListFetch = <T = any, P = any>({
  fetchFunction,
  params,
  immediate = true,
  defaultLoading = true
}: FetchType<T, P>) => {
  const [loading, setLoading] = useState(defaultLoading)
  const [data, setData] = useState<T>()
  const [error, setError] = useState<string>()
  const [isImmediate, setIsImmediate] = useState(immediate)

  const getData = useCallback(async () => {
    try {
      setLoading(true)
      setError(undefined)
      const res = await fetchFunction(params)
      if (res.code === 'success') {
        setData(res.result)
      } else {
        setError(res.message || '请求失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '请求异常')
    } finally {
      setLoading(false)
    }
  }, [params])

  useEffect(() => {
    isImmediate && getData()
    setIsImmediate(true)
  }, [getData])

  return { loading, data, error, getData, setData }
}

export default useListFetch
