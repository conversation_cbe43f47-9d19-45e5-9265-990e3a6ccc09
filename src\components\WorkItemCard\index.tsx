import React, { memo, useMemo, useRef, useState } from 'react'
import { Card, Avatar, Typography, Tooltip, Dropdown, Button, message } from 'antd'
import {
  PlayCircleOutlined,
  ShareAltOutlined,
  LikeOutlined,
  MessageOutlined,
  MoreOutlined,
  DownloadOutlined,
  ReloadOutlined,
  DeleteOutlined,
  SaveOutlined
} from '@ant-design/icons'
import WorkDetial from '@/components/WorkDetial'
import { formatNumber } from '@/utils/delNumber'

const { Text, Title } = Typography

interface WorkItemCardProps {
  itemData: {
    workId: string
    atUniqueId?: string
    authorId: string
    authorName?: string
    avatar?: string
    location?: string
    followCount?: number
    uniqueId: string
    secUid: string
    url: string
    categoryType: number
    thumbnailLink: string
    isAd: number
    title: string
    content: string
    hashtags: string
    likeCount: number
    playCount: number
    commentCount: number
    shareCount: number
    videoUrl: string
    publishTime: number
  }
  type?: 'hot' | 'detail' | 'inspire'
  onViewDetail?: (id: string) => void
}

const WorkItemCard: React.FC<WorkItemCardProps> = ({ itemData, type = 'hot' }) => {
  const {
    workId = 'workId',
    atUniqueId = '',
    title = '视频标题',
    authorName = '作者',
    avatar = '',
    location = '地区',
    followCount = 10,
    likeCount = 10,
    commentCount = 10,
    shareCount = 10,
    videoUrl = 'https://linknext-1307793115.cos.ap-hongkong.myqcloud.com/material/1/20250711/media/7525516711513034039.mp4',
    thumbnailLink = 'https://linknext-1307793115.cos.ap-hongkong.myqcloud.com/material/1/20250711/images/5f93747218b2187c00bc83382dfff4e8.jpeg?imageMogr2/crop/300x400/gravity/center'
  } = itemData
  const [workDetialOpen, setWorkDetialOpen] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const [playing, setPlaying] = useState(false)

  const handlePlay = () => {
    setPlaying(true)
    videoRef.current?.play()
  }

  const handlePause = () => {
    setPlaying(false)
    videoRef.current?.pause()
  }

  // 处理更多操作
  const handleDownload = () => {
    message.success('下载素材功能开发中...')
  }

  const handleUpdate = () => {
    message.success('更新素材功能开发中...')
  }

  const handleDelete = () => {
    message.success('删除素材功能开发中...')
  }

  // 更多操作菜单项
  const getMenuItems = useMemo(() => {
    return [
      {
        key: 'download',
        icon: <DownloadOutlined />,
        label: '下载作品',
        types: ['hot', 'inspire'],
        onClick: handleDownload
      },
      {
        key: 'update',
        icon: <ReloadOutlined />,
        label: '更新作品',
        types: ['inspire'],
        onClick: handleUpdate
      },
      {
        key: 'save',
        icon: <SaveOutlined />,
        label: '保存作品',
        types: ['hot'],
        onClick: handleDelete
      },
      {
        key: 'delete',
        icon: <DeleteOutlined />,
        label: '删除作品',
        types: ['inspire'],
        onClick: handleDelete,
        danger: true
      }
    ].filter(item => !item.types.length || item.types.includes(type))
  }, [itemData.workId])

  return (
    <>
      <Card
        onMouseEnter={handlePlay}
        onMouseLeave={handlePause}
        style={{
          minWidth: 240,
          width: '100%',
          borderRadius: 12,
          overflow: 'hidden',
          position: 'relative'
        }}
        onClick={() => setWorkDetialOpen(true)}
        styles={{
          body: {
            padding: 0
          }
        }}
        hoverable
        actions={[
          <span key="like">
            <Tooltip title="点赞">
              <LikeOutlined />
              <span style={{ marginLeft: 4 }}>{formatNumber(likeCount)}</span>
            </Tooltip>
          </span>,
          <span key="comment">
            <Tooltip title="评论">
              <MessageOutlined />
              <span style={{ marginLeft: 4 }}>{formatNumber(commentCount)}</span>
            </Tooltip>
          </span>,
          <span key="share">
            <Tooltip title="分享">
              <ShareAltOutlined />
              <span style={{ marginLeft: 4 }}>{formatNumber(shareCount)}</span>
            </Tooltip>
          </span>
        ]}
      >
        {/* 右上角更多操作 */}
        {type !== 'detail' && (
          <div onClick={e => e.stopPropagation()}>
            <Dropdown
              menu={{ items: getMenuItems }}
              trigger={['hover', 'click']}
              placement="bottomRight"
            >
              <Button
                type="text"
                icon={<MoreOutlined />}
                style={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  zIndex: 10,
                  color: '#666',
                  border: 'none'
                }}
                size="small"
              />
            </Dropdown>
          </div>
        )}

        {/* 人物介绍区域 */}
        {type !== 'detail' && (
          <div style={{ display: 'flex', alignItems: 'center', padding: '8px 12px' }}>
            <Avatar size={40} src={avatar} />
            <div style={{ marginLeft: 10, flex: 1, minWidth: 0 }}>
              <div
                style={{
                  fontWeight: 500,
                  fontSize: 15,
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}
              >
                {authorName}
              </div>
              <Text type="secondary" style={{ fontSize: 12 }}>
                粉丝：{followCount} · {location}
              </Text>
            </div>
          </div>
        )}
        {/* 视频区域 */}
        <div
          onClick={e => e.stopPropagation()}
          style={{
            position: 'relative',
            marginBottom: 8,
            display: 'flex',
            justifyContent: 'center'
          }}
        >
          <video
            ref={videoRef}
            width="100%"
            height={280}
            controlsList="nodownload"
            disablePictureInPicture
            poster={thumbnailLink}
            style={{ background: '#000', objectFit: 'cover' }}
            controls={playing}
          >
            <source src={videoUrl} type="video/mp4" />
            您的浏览器不支持 video 标签。
          </video>
          {!playing && (
            <div
              onClick={handlePlay}
              style={{
                display: 'flex',
                width: '100%',
                height: '100%',
                position: 'absolute',
                fontSize: 48,
                background: 'rgba(100,100,100,.1)',
                opacity: 0.85,
                cursor: 'pointer',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 2
              }}
            >
              <PlayCircleOutlined
                style={{
                  color: '#fff'
                }}
              />
            </div>
          )}
        </div>
        {/* 视频标题区域 */}
        <Title level={5} ellipsis={{ rows: 1, tooltip: true }} style={{ padding: '0 6px' }}>
          {title}
        </Title>
      </Card>
      {workDetialOpen && (
        <WorkDetial
          open={workDetialOpen}
          onClose={() => setWorkDetialOpen(false)}
          workId={workId}
          atUniqueId={atUniqueId}
        />
      )}
    </>
  )
}

export default memo(WorkItemCard)
