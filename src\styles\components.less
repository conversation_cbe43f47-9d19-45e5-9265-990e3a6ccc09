.lineTabs {
  .renderTabBar {
    z-index: 10;
    padding: 0 22px;
    margin: 0 var(--default-burden-size2);
    background-color: #fff;
    transform: translateY(var(--default-burden-size1));
    > .ant-tabs-nav {
      margin-bottom: 0;
    }
  }
}

.stickySection {
  padding: 0 22px;
  margin: 0 var(--default-burden-size1);
  transform: translateY(var(--default-burden-size1));
  background-color: #fff;
}

.header {
  display: flex;
  justify-content: space-between;
  padding-top: 4px;
  padding-bottom: var(--default-size1);
  .left {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    > div {
      margin-bottom: 0;
    }
  }
  .right {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    > div {
      margin-bottom: 0;
    }
  }
}

.editParagraph {
  inset-inline-start: 0 !important;
  margin-top: 0 !important;
}

.linkText {
  cursor: pointer;
  color: #000000e0 !important;
  &:hover {
    color: #1677ff !important;
  }
}

.rankTable {
  .ant-table-thead .ant-table-cell {
    background-color: #fff;
  }
}

.selectTag {
  margin: 0;
  font-size: 16px;
  line-height: 28px;
  padding: 0 12px;
}

@media screen and (max-width: 768px) {
  .mobileFlex {
    display: flex;
    flex-direction: column;
    gap: 0 !important;
  }
}
