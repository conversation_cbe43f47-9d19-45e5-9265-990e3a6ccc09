import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { Button, Empty, Flex, Spin } from 'antd'
import { ReloadOutlined } from '@ant-design/icons'
import { FilterBarValue } from './FilterBar'
import { WorkItemCard } from '@/components'
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll'
import { getInspireList } from '@/services/inspire'

const PAGE_SIZE = 20

// 定义数据类型
interface InspireItem {
  id: string
  title?: string
  authorName?: string
  avatar?: string
  location?: string
  coverUrl?: string
  videoUrl?: string
  publishTime?: string
  followCount?: string
  likeCount?: string
  commentCount?: string
  shareCount?: string
  playCount?: string
  duration?: string
  tags?: string[]
}

interface InspireListProps {
  filter: FilterBarValue
  onViewDetail?: (id: string) => void
}

// 加载状态组件
const LoadingIndicator: React.FC<{
  loading: boolean
  hasMore: boolean
  error: Error | null
  onRetry: () => void
}> = ({ loading, hasMore, error, onRetry }) => {
  if (error) {
    return (
      <div style={{ width: '100%', textAlign: 'center', padding: 16 }}>
        <div style={{ color: '#ff4d4f', marginBottom: 8 }}>加载失败: {error.message}</div>
        <Button icon={<ReloadOutlined />} onClick={onRetry} size="small">
          重试
        </Button>
      </div>
    )
  }

  if (loading) {
    return (
      <Flex
        style={{ width: '100%', textAlign: 'center', padding: 16 }}
        align="center"
        justify="center"
      >
        <Spin size="small" />
        <span style={{ marginLeft: 8, color: '#888' }}>加载中...</span>
      </Flex>
    )
  }

  if (hasMore) {
    return (
      <div style={{ width: '100%', textAlign: 'center', padding: 16, color: '#888' }}>
        加载更多...
      </div>
    )
  }

  return (
    <div style={{ width: '100%', textAlign: 'center', padding: 16, color: '#888' }}>
      没有更多了~
    </div>
  )
}

// 卡片网格组件
const CardGrid: React.FC<{
  items: InspireItem[]
  onViewDetail?: (id: string) => void
  isEmpty?: boolean
  isInitialLoading?: boolean
}> = React.memo(({ items, onViewDetail, isEmpty, isInitialLoading }) => {
  // 首次加载时显示加载状态
  if (isInitialLoading) {
    return null
  }

  // API 调用完成后没有数据才显示空状态
  if (isEmpty && items.length === 0) {
    return (
      <div
        style={{
          padding: '40px 12px',
          background: '#fff',
          borderRadius: '8px',
          minHeight: '300px'
        }}
      >
        <Empty description="暂无数据" />
      </div>
    )
  }

  return (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill, minmax(240px, 1fr))',
        gap: '12px',
        padding: '12px',
        background: '#fff',
        borderRadius: '8px',
        minHeight: '300px'
      }}
    >
      {items.map(item => (
        <div key={item.id} style={{ minWidth: 240 }}>
          <WorkItemCard itemData={item} type="inspire" />
        </div>
      ))}
    </div>
  )
})

CardGrid.displayName = 'CardGrid'

const InspireList: React.FC<InspireListProps> = ({ filter }) => {
  const [hasMore, setHasMore] = useState(true)

  // 初始数据 - 空数组，将通过API加载
  const initialData = useMemo(() => [], [])

  // 添加一个专门用于监听筛选条件变化的 key
  const filterKey = useMemo(() => {
    return JSON.stringify({
      platform: filter.platform,
      workType: filter.workType,
      tag: filter.tag,
      time: filter.time,
      customRange: filter.customRange,
      searchKeyword: filter.searchKeyword
    })
  }, [filter])

  // 构建 API 请求参数
  const buildApiParams = useCallback(
    (pageNo: number) => {
      const baseParams = {
        pageNo,
        pageSize: PAGE_SIZE,
        platform: filter.platform === 'all' ? undefined : filter.platform,
        search: filter.searchKeyword || undefined
      }

      // 处理时间筛选参数
      if (filter.time === 'all') {
        // 全部：不传时间参数
        return baseParams
      } else if (filter.customRange && filter.customRange[0] && filter.customRange[1]) {
        // 自定义日期范围：传开始和结束时间戳
        return {
          ...baseParams,
          startTime: filter.customRange[0].valueOf(),
          endTime: filter.customRange[1].valueOf()
        }
      } else if (filter.time !== 'custom') {
        // 具体天数：传 days 参数
        const daysMap: { [key: string]: string } = {
          '7d': '7',
          '14d': '14',
          '30d': '30'
        }
        return {
          ...baseParams,
          days: daysMap[filter.time]
        }
      }

      return baseParams
    },
    [filter]
  )

  // 加载更多数据的函数 - 使用真实API
  const loadMoreData = useCallback(
    (currentData: InspireItem[]) => {
      return new Promise<InspireItem[]>((resolve, reject) => {
        // 计算当前页码 - 确保从1开始

        const pageNo = Math.floor(currentData.length / PAGE_SIZE) + 1

        // 构建请求参数
        const apiParams = buildApiParams(pageNo)

        // 调用真实API
        getInspireList(apiParams)
          .then(res => {
            if (res.code === 'success') {
              const { data, hasMore } = res.result

              // 更新是否有更多数据
              setHasMore(hasMore)

              // 返回数据
              resolve(data || [])
            } else {
              reject(new Error(res.msg || '加载失败'))
            }
          })
          .catch(error => {
            reject(new Error(error?.message || '网络连接失败'))
          })
      })
    },
    [buildApiParams]
  )

  // 使用 useInfiniteScroll 处理无限滚动
  const { data, reset, loading, error, loaderRef, retry } = useInfiniteScroll(
    initialData,
    loadMoreData,
    hasMore,
    [filterKey], // 使用 filterKey 确保筛选条件变化时能正确触发
    { threshold: 0.1, rootMargin: '100px' }
  )

  // 首次加载状态
  const [initialLoading, setInitialLoading] = useState(true)

  // 数据加载完成后，设置初始加载状态为 false
  useEffect(() => {
    if (data.length > 0 || (data.length === 0 && !loading && !hasMore)) {
      setInitialLoading(false)
    }
  }, [data, loading, hasMore])

  // 筛选条件变化时重置列表并重新加载
  useEffect(() => {
    // 重置所有状态
    setHasMore(true)
    setInitialLoading(true)

    // 立即重置数据，这会触发 useInfiniteScroll 重新加载
    reset([])
  }, [filterKey, reset])

  return (
    <>
      <CardGrid
        items={data}
        isInitialLoading={initialLoading}
        isEmpty={!initialLoading && data.length === 0}
      />
      <div ref={loaderRef}>
        <LoadingIndicator loading={loading} hasMore={hasMore} error={error} onRetry={retry} />
      </div>
    </>
  )
}

export default InspireList
