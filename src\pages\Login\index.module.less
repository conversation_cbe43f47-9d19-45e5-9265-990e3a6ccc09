.header {
  position: fixed;
  top: 10px;
  left: 0;
  right: 0;
  z-index: 5;
}

.container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(to top, rgb(50, 102, 255), rgb(185, 203, 255), rgb(244, 245, 255));
  width: 100vw;
  height: 100vh;
  overflow: auto;
}

.leftSection {
  flex: 3;
}

.rightSection {
  flex: 7;
  position: relative;
  z-index: 5;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  height: 100vh;
  .loginContainer {
    display: flex;
    flex-direction: column;
    border-radius: 8px;
    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.1);
  }
}

.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: absolute;
  z-index: 5;
  bottom: 12px;
  text-align: center;
  gap: 12px;
  a {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    color: #677887;
    img {
      width: 16px;
    }
  }
}

@media screen and (max-width: 768px) {
  .leftSection {
    flex: 0;
  }

  .rightSection {
    flex: 1;
  }

  .footer {
    flex-direction: column;
    gap: 6px;
  }
}

@media screen and (max-width: 440px) {
  .footer {
    display: none;
  }

  .rightSection {
    height: 100vh;
    .loginContainer {
      width: 100%;
      height: 100%;
    }
  }
}
