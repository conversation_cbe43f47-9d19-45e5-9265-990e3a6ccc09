import { CORE } from '@/constants/service'
import http, { ResponseResult } from '@/utils/http'

// 小红书
/*
 * 小红书授权列表
 */
export const getRedBookList = (
  data: {
    dateType: string | number
    search?: string
  } & Global.PageParams
): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/redbookCreator/account/list`,
    method: 'post',
    data
  })
}

/*
 * 扫描授权信息
 */
export const getRedBookQrCodeInfo = (params: { uuid: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/redbook/creator/auth/info`,
    method: 'get',
    params
  })
}

/*
 * 新增授权账号
 */
export const newRedBookQrCode = (): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/redbook/creator/qrcode/new`,
    method: 'post',
    data: {
      qrAuthType: 'qr_code_redbook'
    }
  })
}

/*
 * 重新授权
 */
export const reauthRedBookQrCode = (data: { uuid: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/redbook/creator/qrcode/reauth`,
    method: 'post',
    data
  })
}

/*
 * 验证码授权
 */
export const updateRedBookSmscode = (data: {
  uuid: string
  smsCode: string
}): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/redbook/creator/update/smscode`,
    method: 'post',
    data
  })
}

/*
 * 小红书账号通知设置详情
 */
export const queryRedBookNotify = (): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/account/notify/query`,
    method: 'get',
    params: { accountType: 'REDBOOK' }
  })
}

/*
 * 设置小红书账号通知设置
 */
export const updateRedBookNotify = (data: { notifyRules: any }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/account/notify/update`,
    method: 'post',
    data: { ...data, accountType: 'REDBOOK' }
  })
}

/*
 * 小红书账号通知设置状态
 */
export const updateRedBookNotifyStatus = (params: { status: number }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/account/notify/updateStatus`,
    method: 'get',
    params: {
      ...params,
      accountType: 'REDBOOK'
    }
  })
}

/*
 * 删除小红书账号
 */
export const deleteRedBookAccount = (params: { uuid: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/redbook/creator/auth/delete`,
    method: 'get',
    params
  })
}

/*
 * 获取公域小红书授权链接
 */
export const getRedBookAccountAuthUrl = (): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/redbookCreator/authUrl`,
    method: 'get'
  })
}

/*
 * 获取公域小红书授权链接
 */
export const newAuthRedBookQrCode = (params: { code: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/redbook/creator/authUrl/new`,
    method: 'get',
    params
  })
}

/*
 * 公域 - 获取小红书登陆信息
 */
export const getRedBookAuthUrlInfo = (params: { uuid: string }): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/redbook/creator/authUrl/info`,
    method: 'get',
    params
  })
}
