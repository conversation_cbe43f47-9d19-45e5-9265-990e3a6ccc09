import React, { useEffect, useRef, useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lex, Spin, Result } from 'antd'
import { BulbOutlined, FileTextOutlined, LoadingOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import MarkDown from '@/components/MarkDown'
import { ResponseResult } from '@/utils/http'
import {
  getWorkQuotesTaskResult,
  addWorkQuotesDisassembly,
  getWorkVideoTaskResult,
  addWorkVideoDisassembly
} from '@/services/workDetail'
import { message } from '@/components/GlobalTips'
import { workDetailStore } from '@/store'
import { formatSecondsToTime } from '@/utils/delNumber'
import styles from './AIExtraction.module.less'
import { isNumber } from 'lodash-es'

const { Title } = Typography

type FetchParams = {
  workId: string
  atUniqueId?: string
}

interface AnalysisType {
  params: FetchParams
  getAnalysisTask: (params: FetchParams) => Promise<ResponseResult<any>>
  addAnalysisTask: (params: FetchParams) => Promise<ResponseResult<any>>
}

enum TaskStatus {
  Running = 0,
  Success = 1,
  Fail = 2,
  Init = 3
}

const useAnalysis = ({ getAnalysisTask, addAnalysisTask, params }: AnalysisType) => {
  const [intLoading, setIntLoading] = React.useState(true)
  const [loading, setLoading] = React.useState(false)
  const [taskStatus, setTaskStatus] = useState(3)
  const [analysisData, setAnalysisData] = React.useState<any>()
  const timer = useRef<number | null>(null)

  const clearTimer = () => {
    if (timer.current) {
      clearTimeout(timer.current)
      timer.current = null
    }
  }

  const addWorkDisassemblyTask = () => {
    setLoading(true)
    addAnalysisTask(params)
      .then(res => {
        if (res.code === 'success') {
          clearTimer()
          getWorkTaskResultData()
        } else {
          message.error(res.msg || '分析失败')
          setLoading(false)
        }
      })
      .catch(error => {
        console.error('分析失败:', error)
      })
  }

  const getWorkTaskResultData = () => {
    clearTimer()
    getAnalysisTask(params)
      .then(res => {
        if (res.code === 'success') {
          const { result, status = 3 } = res.result
          if (result && analysisData !== result) {
            setAnalysisData(result)
          }
          if (status !== taskStatus && isNumber(status)) {
            setTaskStatus(status)
          }
          if (status === TaskStatus.Running && !timer.current) {
            timer.current = window.setTimeout(() => getWorkTaskResultData(), 10000)
          }
        } else {
          message.error(res.msg || '获取分析结果失败')
        }
      })
      .finally(() => {
        setLoading(false)
        setIntLoading(false)
      })
  }

  useEffect(() => {
    getWorkTaskResultData()
    return () => {
      clearTimer()
    }
  }, [])

  return {
    loading,
    taskStatus,
    intLoading,
    analysisData,
    addTask: addWorkDisassemblyTask
  }
}

const IntLoading = () => {
  return (
    <div className={styles.section} style={{ textAlign: 'center' }}>
      <Spin />
    </div>
  )
}

const RunningStatus = () => (
  <Result icon={<LoadingOutlined />} title="分析中。。。" subTitle="预计1-3分钟请稍等..." />
)

const FailStatus = ({ addTask, loading }: any) => (
  <Result
    status="error"
    title="生成失败"
    extra={
      <Button type="primary" onClick={addTask} loading={loading}>
        重新分析
      </Button>
    }
  />
)

// 台词提取
const QuotesSection = observer(() => {
  const { workParams, currentVideoTime, isVideoPlaying } = workDetailStore
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const lastHighlightedIndex = useRef<number>(-1)

  const {
    loading,
    analysisData = [],
    intLoading,
    taskStatus,
    addTask
  } = useAnalysis({
    params: workParams!,
    getAnalysisTask: getWorkQuotesTaskResult,
    addAnalysisTask: addWorkQuotesDisassembly
  })

  // 处理时间点击跳转
  const handleTimeClick = (time: number) => {
    workDetailStore.jumpToVideoTime(time)
  }

  // 判断当前时间段是否高亮
  const isCurrentTimeSegment = (start: number, end: number) => {
    return currentVideoTime >= start && currentVideoTime <= end
  }

  // 获取当前高亮的台词索引
  const getCurrentHighlightedIndex = () => {
    return (
      analysisData?.findIndex((item: any) => isCurrentTimeSegment(item.startTime, item.endTime)) ??
      -1
    )
  }

  // 自动滚动到当前高亮的台词
  const scrollToCurrentItem = (index: number) => {
    if (index === -1 || !scrollContainerRef.current) {
      return
    }

    const container = scrollContainerRef.current
    const items = container.querySelectorAll('[data-quote-index]')
    const targetItem = items[index] as HTMLElement

    if (targetItem) {
      // 使用 scrollIntoView 方法，更简洁且兼容性更好
      targetItem.scrollIntoView({
        behavior: 'smooth',
        block: 'center', // 将元素滚动到容器的中心位置
        inline: 'nearest'
      })
    }
  }

  // 监听视频时间变化，自动滚动
  useEffect(() => {
    // 只有在视频播放时才自动滚动
    if (!isVideoPlaying) {
      return
    }

    const currentIndex = getCurrentHighlightedIndex()

    // 只有当高亮项发生变化时才滚动
    if (currentIndex !== -1 && currentIndex !== lastHighlightedIndex.current) {
      lastHighlightedIndex.current = currentIndex

      // 使用 requestAnimationFrame 确保在下一帧执行滚动，避免性能问题
      requestAnimationFrame(() => {
        scrollToCurrentItem(currentIndex)
      })
    }
  }, [currentVideoTime, isVideoPlaying, analysisData])

  // 重置高亮索引当视频暂停时
  useEffect(() => {
    if (!isVideoPlaying) {
      lastHighlightedIndex.current = -1
    }
  }, [isVideoPlaying])

  if (intLoading) {
    return <IntLoading />
  }

  return (
    <div className={styles.section}>
      <Flex className={styles.sectionHeader}>
        <Title level={5} className={styles.sectionTitle}>
          <BulbOutlined />
          台词提取
        </Title>
        {/* <Flex gap={12} align="center">
          <Button type="primary">台词下载</Button>
          <Button>台词翻译</Button>
          <Button>复制台词</Button>
        </Flex> */}
      </Flex>
      {taskStatus === TaskStatus.Running && <RunningStatus />}
      {/* 分析结果区域 */}
      {taskStatus === TaskStatus.Success && (
        <Flex gap={12} vertical ref={scrollContainerRef} className={styles.quotesScrollContainer}>
          {analysisData?.map((item: any, index: number) => {
            const isHighlighted = isCurrentTimeSegment(item.start, item.end)
            return (
              <Flex
                key={item.id}
                vertical
                gap={6}
                data-quote-index={index}
                className={`${styles.quotesTimeSegment} ${isHighlighted ? styles.highlighted : ''}`}
                onClick={() => handleTimeClick(item.startTime)}
              >
                <div className={styles.quotesTimeText}>
                  {formatSecondsToTime(item.startTime)}~{formatSecondsToTime(item.endTime)}
                </div>
                <div className={styles.quotesText}>{item.content}</div>
              </Flex>
            )
          })}
        </Flex>
      )}
      {taskStatus === TaskStatus.Fail && <FailStatus />}
      {taskStatus === TaskStatus.Init && (
        <div className={styles.extractionCard}>
          <Flex gap={12} style={{ margin: 'auto' }}>
            <div className={styles.extractionIcon}>
              <BulbOutlined style={{ fontSize: 24, color: '#1677ff' }} />
            </div>
            <div className={styles.extractionContent}>
              <div className={styles.extractionTitle}>一键提取台词脚本</div>
              <div className={styles.extractionDesc}>一键提取视频脚本</div>
              <div className={styles.extractionDesc}>台词文案素材库</div>
              <div className={styles.extractionDesc}>台词文案素材库</div>
              <Button
                type="primary"
                className={styles.extractionButton}
                loading={loading}
                onClick={addTask}
              >
                立即提取
              </Button>
            </div>
          </Flex>
        </div>
      )}
    </div>
  )
})

const VideoSection = () => {
  const { workParams } = workDetailStore
  const {
    loading,
    analysisData = `### 一、视频速览
- **视频主题**：一位家长分享为6岁孩子制作无咖啡因、富含营养的“儿童咖啡”的过程及好处。
- **目标受众**：
    - **核心群体画像**：有孩子且关注孩子健康饮食的家长，年龄在25 - 40岁之间，注重生活品质和孩子的营养摄入。
    - **延伸群体画像**：对健康饮品感兴趣的人群，以及想要节省开支的消费者。
- **提供价值**：
    - **功能价值**：提供了一种健康的儿童饮品制作方法，无咖啡因、无糖且富含纤维和维生素；帮助家长节省购买儿童咖啡饮品的开支。
    - **情绪价值**：满足家长对孩子健康的关爱需求，让孩子也能享受类似咖啡的饮品带来的快乐。
- **内容选题**：切入有孩子且关注孩子健康饮食的家长群体的刚需痛点，锚定家庭场景中孩子想要喝咖啡但又不能摄入咖啡因的问题，属于真实场景复现。
- **内容形式**：博主真人出镜制作饮品+口播。

### 二、价值判断
- **选题价值**：切中家长担心孩子摄入咖啡因不健康，又想满足孩子喝咖啡愿望的痛点，同时考虑到经济成本。在场景契合度上较高，唯一不足可能是未提及更多孩子不适合喝咖啡的原因。
- **爆点分析**：引发观众情绪爆点的地方在于展示孩子喝到“儿童咖啡”的画面，让家长感同身受。叙事逻辑上，在介绍产品营养成分时爆点设计比较薄弱。
- **内容节奏**：整体叙事节奏流畅，从孩子想喝咖啡的问题引出制作过程，再到展示成品和孩子饮用画面。
- **画面台词**：台词风格亲切自然，像和朋友分享经验；画面设计以厨房为背景，展示制作过程清晰直观，效果较好。
- **转化链路**：通过分享制作过程和产品优势，可能引导观众购买相关制作材料或类似儿童健康饮品产品。
- **价值总结**：核心价值亮点在于提供健康省钱的儿童饮品解决方案。模仿者可选取生活中孩子的常见需求场景，详细展示制作过程并突出产品优势。

### 三、结构拆解
1. **黄金3S**：
    - **效果**：引发好奇。
    - **前3S台词**：Every morning, my 6 year old runs into the kitchen yelling, I want coffee.
2. **脚本结构**：
    - **脚本框架**：提出问题+制作展示+成品分享+推荐引导
    - **脚本镜头**：
        - **时段**：0 - 7s
        - **叙事元素**：提出问题
        - **效果主题**：孩子每天早上吵着要喝咖啡，家长面临如何满足孩子需求的问题。
        - **画面描述**：博主站在厨房中，背景是白色橱柜和电器，博主穿着白色带图案上衣和灰色短裙，开始讲述每天早上6岁孩子冲进厨房喊着要喝咖啡的场景，表情自然。
        - **台词**：Every morning, my 6 year old runs into the kitchen yelling, I want coffee. And yeah, I could say no, but instead I started making him his own version with kid coffee.
        - **时段**：7 - 13s
        - **叙事元素**：产品介绍
        - **效果主题**：介绍自制“儿童咖啡”的优势，无咖啡因、无糖且富含营养。
        - **画面描述**：博主拿起制作材料展示，包括一盒饮品原料和一瓶蛋白牛奶，展示原料包装上的营养成分表，脸上带着自信的微笑。
        - **台词**：It looks like mine, smells like mine, but there's no caffeine, no sugar, and it's packed with fiber and vitamins.
        - **时段**：13 - 19s
        - **叙事元素**：制作展示
        - **效果主题**：详细展示制作“儿童咖啡”加入蛋白牛奶等材料的过程。
        - **画面描述**：博主将蛋白牛奶倒入玻璃罐中，接着加入饮品原料，拿出搅拌器进行搅拌，动作娴熟，展示制作的轻松和便捷。
        - **台词**：I mix it with protein milk for that extra boost, and he genuinely thinks he's getting a Carmel latte, but I know he's starting his day with something that's actually good for him.
        - **时段**：19 - 22s
        - **叙事元素**：成本优势
        - **效果主题**：强调自制饮品相比外买更省钱。
        - **画面描述**：博主停下手中动作，对着镜头说话，表情轻松愉快。
        - **台词**：And I'm not dropping 5:00 50 at the drive through anymore.
        - **时段**：22 - 25s
        - **叙事元素**：成品分享
        - **效果主题**：展示制作完成的“儿童咖啡”成品，外观诱人。
        - **画面描述**：博主拿起装满“儿童咖啡”的玻璃罐，罐中饮品颜色诱人，上面还挤了奶油，博主展示成品，脸上带着满意的笑容。
        - **台词**：We genuinely love this stuff.
        - **时段**：25 - 26s
        - **叙事元素**：推荐引导
        - **效果主题**：向有同样需求的家长推荐自制“儿童咖啡”。
        - **画面描述**：博主拿着成品，对着镜头说话，眼神真诚。
        - **台词**：If your kid wants coffee too, this is the one to get. `,
    intLoading,
    taskStatus,
    addTask
  } = useAnalysis({
    params: workParams!,
    getAnalysisTask: getWorkVideoTaskResult,
    addAnalysisTask: addWorkVideoDisassembly
  })

  if (intLoading) {
    return <IntLoading />
  }

  return (
    <div className={styles.section}>
      <Flex className={styles.sectionHeader}>
        <Title level={5} className={styles.sectionTitle}>
          <FileTextOutlined />
          视频解构
        </Title>
        {/* <Flex gap={12} align="center">
          <Button type="primary">脚本仿写改写</Button>
          <Button>下载报告</Button>
        </Flex> */}
      </Flex>
      {taskStatus === TaskStatus.Running && <RunningStatus />}
      {taskStatus === TaskStatus.Success && (
        <MarkDown content={analysisData} className={styles.markdownContent} />
      )}
      {taskStatus === TaskStatus.Fail && <FailStatus />}
      {taskStatus === TaskStatus.Init && (
        <div className={styles.extractionCard}>
          <Flex gap={12} style={{ margin: 'auto' }}>
            <div className={styles.extractionIcon}>
              <FileTextOutlined style={{ fontSize: 24, color: '#1677ff' }} />
            </div>
            <div className={styles.extractionContent}>
              <div className={styles.extractionTitle}>视频解构分析</div>
              <div className={styles.extractionDesc}>视频分镜头解构</div>
              <div className={styles.extractionDesc}>镜头元素解构分析</div>
              <div className={styles.extractionDesc}>电商带货——爆款分析</div>
              <Button
                type="primary"
                className={styles.extractionButton}
                loading={loading}
                onClick={addTask}
              >
                立即分析
              </Button>
            </div>
          </Flex>
        </div>
      )}
    </div>
  )
}

const AIExtraction = () => {
  return (
    <div className={styles.aiExtractionContent}>
      {/* 台词提取 */}
      <QuotesSection />
      {/* 视频解构 */}
      {/* <VideoSection /> */}
    </div>
  )
}

export default AIExtraction
