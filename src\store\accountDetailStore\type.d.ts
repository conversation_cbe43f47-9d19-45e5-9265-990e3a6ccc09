import { AccountDetailStore as AccountDetailStoreModel } from './index'

export as namespace IAccountDetailStore

export type AccountDetailStore = AccountDetailStoreModel

// 作者基本信息
export interface AccountDetail {
  atUniqueId: string
  homeUrl: string
  avatar: string
  name: string
  platform: string
  desc: string
  registryLocation: string
  workCount: number
  fansCount: number
  likeCount: number
  registerTime: number
  createTime: number
  lastSyncTime: number
  latestPublishTime: number
  today: {
    addFansCount: number
    addLikeCount: number
    addResourceCount: number
    addUsingResourceCount: number
    addPlayCount: number
    addCommentCount: number
    addShareCount: number
    addCollectCount: number
    followingCount: number
    addFollowingCount: number
  }
  yesterday: {
    addFansCount: number
    addLikeCount: number
    addResourceCount: number
    addUsingResourceCount: number
    addPlayCount: number
    addCommentCount: number
    addShareCount: number
    addCollectCount: number
    followingCount: number
    addFollowingCount: number
  }
}

// Store状态
export interface AccountDetailState {
  loading: boolean
  accountDetail: WorkDetail | null
  activeTab: string
  error: string | null
}
