import React, { memo, useState } from 'react'
import { Button } from 'antd'
import { message } from '../GlobalTips'
import { ResponseResult } from '@/utils/http'
import { DownloadOutlined } from '@ant-design/icons'

type ExportDownLoadPropsType = {
  fetchFunction: (params?: any) => Promise<ResponseResult>
  params?: any
}

const ExportDownLoad: React.FC<ExportDownLoadPropsType> = ({ fetchFunction, params }) => {
  const [downLoading, setDownLoading] = useState(false)
  const downLoad = () => {
    setDownLoading(true)
    fetchFunction(params)
      .then(res => {
        if (res.code === 'success') {
          window.open(res.result)
          message.success('下载成功')
        } else {
          message.error(res.msg)
        }
      })
      .finally(() => {
        setDownLoading(false)
      })
  }
  return (
    <Button loading={downLoading} icon={<DownloadOutlined />} type="primary" onClick={downLoad}>
      下载
    </Button>
  )
}

export default memo(ExportDownLoad)
