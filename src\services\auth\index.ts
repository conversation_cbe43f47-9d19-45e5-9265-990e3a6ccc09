import { CORE } from '@/constants/service'
import { AESEncrypt } from '@/utils/aes'
import http, { ResponseResult } from '@/utils/http'
const AESEncryptPwd = (pwd: string) => {
  return AESEncrypt(pwd, '83c933c49a5179dd1a8b9d6111111111', '3kf93mg02ga11111')
}

/*
 *登录
 */
export function loginService(payload: any): Promise<ResponseResult> {
  if (payload.password) {
    payload.password = AESEncryptPwd(payload.password)
  }
  return http.request({
    url: `${CORE}/user/login`,
    method: 'post',
    data: payload,
    efficient: true,
    ignore: true
  })
}

/*
 *注册
 */
export function registerService(payload: any): Promise<ResponseResult> {
  if (payload.password) {
    payload.password = AESEncryptPwd(payload.password)
  }
  return http.request({
    url: `${CORE}/user/register`,
    method: 'post',
    data: payload,
    efficient: true,
    ignore: true
  })
}

/*
 * 退出
 */
export function loginOutService(): Promise<ResponseResult> {
  return http.request({
    url: `${CORE}/user/logout`,
    method: 'get',
    ignore: true
  })
}

/*
 *获取验证码
 */
export const getMessageCode = (payload: {
  email?: string
  phone?: string
  type: number
}): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/user/message`,
    method: 'post',
    data: payload
  })
}

/** 获取当前登录用户信息 */
export async function getCurrentUserInfo(): Promise<ResponseResult> {
  return http.request({
    url: `${CORE}/user/info`,
    efficient: true,
    method: 'get'
  })
}

/*
 * 修改密码
 */
export const updatePassword = (data: {
  phone: string
  password: string
}): Promise<ResponseResult> => {
  if (data.password) {
    data.password = AESEncryptPwd(data.password)
  }
  return http.request({
    url: `${CORE}/user/update/password`,
    method: 'post',
    data
  })
}

/*
 * 修改用户信息
 */
export const updateUserInfoService = (data: {
  avatar?: string
  nickname?: string
}): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/user/update/userinfo`,
    method: 'post',
    data
  })
}

/*
 * 用户获取api token
 */
export const getAipToken = (): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/user/apitoken/query`,
    method: 'get'
  })
}

/*
 * 用户获取刷新api token
 */
export const refreshAipToken = (): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/user/apitoken/refresh`,
    method: 'get'
  })
}

/*
 * 获取api toke用量
 */
export const getApipTokenQuota = (): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/user/apitoken/quota`,
    method: 'get',
    ignore: true
  })
}
