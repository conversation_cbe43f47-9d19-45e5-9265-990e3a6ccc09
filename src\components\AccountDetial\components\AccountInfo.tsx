import React from 'react'
import { Avatar, Typography } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import { accountDetailStore } from '@/store'
import {
  formatNumber,
  formatTimestamp,
  formatEmptyData,
  formatTimeDifference
} from '@/utils/delNumber'
import styles from './AccountInfo.module.less'

const { Text, Link } = Typography

const AccountInfo: React.FC = () => {
  const { accountDetailData } = accountDetailStore
  if (!accountDetailData) {
    return null
  }

  return (
    <div className={styles.accountInfo}>
      <div className={styles.accountHeader}>
        <Link target="_blank" href={accountDetailData.homeUrl}>
          <Avatar size={64} src={accountDetailData.avatar} icon={<UserOutlined />} />
        </Link>
        <div className={styles.accountMeta}>
          <div className={styles.accountNameRow}>
            <div>
              <Link
                target="_blank"
                href={accountDetailData.homeUrl}
                style={{ fontWeight: 600, fontSize: 20, color: '#000' }}
              >
                {accountDetailData.name}
              </Link>
            </div>
            <div className={styles.accountTags}>
              <span className={styles.locationTag}>
                地区：{formatEmptyData(accountDetailData.registryLocation)}
              </span>
              <span className={styles.timeTag}>
                添加监控时间：{formatTimestamp(accountDetailData.createTime)}
              </span>
            </div>
          </div>
          <div className={styles.accountStats}>
            <div className={styles.statGroup}>
              <span className={styles.statLabel}>作品</span>
              <span className={styles.statValue}>{formatNumber(accountDetailData.workCount)}</span>
            </div>
            <div className={styles.statDivider}>|</div>
            <div className={styles.statGroup}>
              <span className={styles.statLabel}>粉丝</span>
              <span className={styles.statValue}>{formatNumber(accountDetailData.fansCount)}</span>
            </div>
            <div className={styles.statDivider}>|</div>
            <div className={styles.statGroup}>
              <span className={styles.statLabel}>获赞</span>
              <span className={styles.statValue}>{formatNumber(accountDetailData.likeCount)}</span>
            </div>
            <div className={styles.statDivider}>|</div>
            <div className={styles.statGroup}>
              <span className={styles.statLabel}>账号年龄</span>
              <span className={styles.statValue}>
                {formatTimeDifference(accountDetailData.registerTime)}
              </span>
            </div>
            <div className={styles.statDivider}>|</div>
            <div className={styles.statGroup}>
              <span className={styles.statLabel}>最近更新时间</span>
              <span className={styles.statValue}>
                {formatTimestamp(accountDetailData.latestPublishTime)}
              </span>
            </div>
          </div>
          <div className={styles.accountDescription}>
            <Text>{accountDetailData.desc}</Text>
          </div>
        </div>
      </div>
    </div>
  )
}

export default observer(AccountInfo)
