import React, { useMemo, Suspense, useEffect, useState } from 'react'
import { Link, matchRoutes, Outlet, useLocation } from 'react-router-dom'
import { isObject } from 'lodash-es'
import { Dropdown, MenuProps } from 'antd'
import { ProLayout } from '@ant-design/pro-components'
import Settings from '@config/defaultSettings'
import { observer } from 'mobx-react-lite'
import NProgress from 'nprogress'
import cs from 'classnames'
import { EditOutlined, LogoutOutlined, UserOutlined } from '@ant-design/icons'
import { authStore, globalStore } from '@/store'
import { RouteType, router, routers } from '@/routers'
import useNavigateRevise from '@/hooks/useNavigateRevise'
import { Loading } from '@/components'
import { UpdatePwdModule } from '@/components/ModalSection'
import { modal } from '@/components/GlobalTips'
import logo from '@/assets/imgae/logo.png'
import './index.less'

/** 处理菜单权限隐藏菜单 */
const reduceRouter = (_routers: RouteType[]): RouteType[] => {
  return _routers
    ?.map(item => {
      const { children, ...extra } = item
      let curData: any
      if (item?.permissionObj?.roles) {
        curData = authStore.menuRoles.filter(
          role => role.permiCode === item.permissionObj!.roles
        )[0]
      }

      return {
        ...extra,
        routes: children ? reduceRouter(children) : null,
        name: curData ? curData?.permiName : item?.name,
        sort: curData ? curData?.showNum : item?.sort,
        hideInMenu:
          item?.hideInMenu ||
          (isObject(item.permissionObj) &&
            item.permissionObj.roles &&
            !authStore.roles.includes(item.permissionObj.roles))
      }
    })
    .sort((a, b) => (a?.sort || 1) - (b?.sort || 0)) as any
}

export enum ComponTypeEnum {
  MENU,
  PAGE,
  COMPON
}

const BasicLayout: React.FC = () => {
  const [pathname, setPathname] = useState(window.location.pathname)
  const _navigate = useNavigateRevise()
  const location = useLocation()
  const matchRoute = matchRoutes(routers, location)
  const [showMenu, setShowMenu] = useState(!matchRoute?.[matchRoute?.length - 1]?.route?.hideLayout)
  const [collapsed, setCollapsed] = useState(() => {
    return localStorage.getItem('collapsed') === 'true'
  })
  const menus = useMemo(() => reduceRouter([router?.routes[1]])[0], [])
  useEffect(() => {
    globalStore.contentSectionDom || globalStore.getContentSectionDom()
    setShowMenu(!matchRoute?.[matchRoute?.length - 1]?.route?.hideLayout)
    setPathname(location.pathname)
    return () => {
      NProgress.done()
    }
  }, [location.pathname])

  const items: MenuProps['items'] = [
    {
      key: 'updatePwd',
      icon: <EditOutlined />,
      onClick: () => {
        authStore.openUpdatePwdModule()
      },
      label: '修改密码'
    },
    {
      key: 'person',
      icon: <UserOutlined />,
      onClick: () => {
        _navigate('/person')
      },
      label: '个人信息'
    },
    {
      key: 'out',
      icon: <LogoutOutlined />,
      onClick: () => {
        modal.confirm({
          title: '退出登录',
          content: '是否退出登录?',
          onOk: () => {
            return authStore.logout()
          }
        })
      },
      label: '退出登录'
    }
  ]

  return (
    <ProLayout
      {...Settings}
      breakpoint={false}
      defaultCollapsed={collapsed}
      collapsed={collapsed}
      onCollapse={() => {
        setCollapsed(!collapsed)
        localStorage.setItem('collapsed', String(!collapsed))
      }}
      contentStyle={{
        height: '100vh',
        overflow: 'hidden'
      }}
      locale="zh-CN"
      route={showMenu ? menus : undefined}
      location={{
        pathname
      }}
      menu={{
        autoClose: false
      }}
      headerTitleRender={() => {
        return (
          <Link to="/">
            <img src={logo} />
          </Link>
        )
      }}
      logo={logo}
      avatarProps={{
        icon: authStore.userInfo.avatar ? (
          <img src={authStore.userInfo.avatar} alt="头像" />
        ) : (
          <UserOutlined style={{ color: '#fff', backgroundColor: '#4080ff', fontSize: '28px' }} />
        ),
        size: 'large',
        style: {
          backgroundColor: '#fff'
        },
        title: authStore.userInfo?.nickname,
        render: (_, defaultDom) => {
          return (
            <Dropdown placement="bottomRight" menu={{ items }}>
              {defaultDom}
            </Dropdown>
          )
        }
      }}
      menuItemRender={(item, dom) => {
        return (
          <a href={item.path!} onClick={e => e.preventDefault()}>
            {dom}
          </a>
        )
      }}
      menuProps={{
        onClick: ({ key }) => {
          globalStore.resetContentSectionDom()
          _navigate(key || '/')
        }
      }}
      token={{
        bgLayout:
          'linear-gradient(to top, rgb(50, 102, 255), rgb(185, 203, 255), rgb(244, 245, 255))',
        pageContainer: {
          paddingBlockPageContainerContent: 0,
          paddingInlinePageContainerContent: 0
        },
        sider: {
          colorTextMenu: '#595959',
          colorBgMenuItemActive: 'rgba(230,243,254,1)',
          colorTextMenuActive: 'rgba(42,122,251,1)',
          colorTextMenuItemHover: 'rgba(42,122,251,1)',
          colorBgMenuItemHover: 'rgba(230,243,254,1)',
          colorTextMenuSelected: 'rgba(42,122,251,1)',
          colorBgMenuItemSelected: 'rgba(230,243,254,1)'
        },
        header: {
          colorBgHeader: 'var(--bg-header-color)'
        }
      }}
      className={cs('basicLayout', {
        'basicLayout-collapsed': collapsed
      })}
      suppressSiderWhenMenuEmpty
    >
      <div id="contentSection">
        <div style={{ flex: 1 }}>
          <Suspense fallback={<Loading />}>
            <Outlet />
          </Suspense>
        </div>
      </div>
      <UpdatePwdModule />
    </ProLayout>
  )
}

export default observer(BasicLayout)
