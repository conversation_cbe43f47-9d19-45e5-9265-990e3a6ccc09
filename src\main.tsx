import ReactDOM from 'react-dom/client'
import { RouterProvider } from 'react-router-dom'
import { ConfigProvider, App as AntApp } from 'antd'
import { router } from '@/routers'

import { StoreProvider, GlobalTips } from '@/components'
import dayjs from 'dayjs'
import './styles/normalize.less'
import 'dayjs/locale/zh-cn'
import zhCN from 'antd/locale/zh_CN'
import 'nprogress/nprogress.css'
dayjs.locale('zh-cn')

ReactDOM.createRoot(document.getElementById('app') as HTMLElement).render(
  <StoreProvider>
    <ConfigProvider locale={zhCN} theme={{ cssVar: true }}>
      <AntApp message={{ maxCount: 1 }}>
        <RouterProvider future={{ v7_startTransition: true }} router={router} />
        <GlobalTips />
      </AntApp>
    </ConfigProvider>
  </StoreProvider>
)
