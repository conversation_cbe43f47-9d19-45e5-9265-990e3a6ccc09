import { authInfoStorage } from '@/utils/storage'

type ISendData = {
  event: string
  authorization?: string
}

export default class Ws {
  // WebSocket 对象
  socket?: WebSocket

  // 心跳计时器
  pingTimeout: any

  // 重连间隔，单位：毫秒
  reconnectTimeout = 1000

  url = ''

  receiveMessageCallback: (msg: ISocketStore.Message) => void

  constructor(config: { url: string; receiveMessageCallback: any }) {
    // WebSocket 连接地址
    this.url = config.url
    // 接收消息回调函数
    this.receiveMessageCallback = config.receiveMessageCallback
  }

  get authorization() {
    return authInfoStorage.get()?.token
  }

  isSocketOpen() {
    return this.socket && this.socket.readyState === this.socket.OPEN
  }

  /**
   * 开启WebSocket
   */
  async start() {
    if (this.isSocketOpen()) {
      console.error('请先断开已有的连接!!!')
    } else {
      this.connectWebSocket()
    }
  }

  /**
   * WebSocket连接
   */
  connectWebSocket() {
    this.socket = new WebSocket(this.url)
    // 处理连接打开事件
    this.socket.addEventListener('open', () => {
      if (this.authorization) {
        this.sendMessage({
          event: 'AUTHORIZATION',
          authorization: this.authorization
        })
        // 心跳机制
        this.startHeartbeat()
      }
    })

    // 处理接收到消息事件
    this.socket.addEventListener('message', event => {
      this.receiveMessage(event as unknown as ISocketStore.Message)
    })

    // 处理连接关闭事件
    this.socket.addEventListener('close', event => {
      // 清除定时器
      clearTimeout(this.pingTimeout)
      clearTimeout(this.reconnectTimeout)
    })

    // 处理 WebSocket 错误事件
    this.socket.addEventListener('error', event => {
      console.log(event)
    })
  }

  /**
   * 启动心跳机制
   */
  startHeartbeat() {
    this.pingTimeout = setInterval(() => {
      // 发送心跳消息
      this.sendMessage({
        event: 'WEB_HEARTBEAT',
        authorization: this.authorization
      })
    }, 30000)
  }

  /**
   * 发送消息
   */
  sendMessage(data: ISendData) {
    if (this.socket!.readyState === WebSocket.OPEN) {
      this.socket!.send(typeof data === 'object' ? JSON.stringify(data) : data)
    } else {
      console.error(
        'WebSocketManager error: WebSocket connection is not open. Unable to send message.'
      )
    }
  }

  /**
   * 接收到消息
   */
  receiveMessage(event: ISocketStore.Message) {
    this.receiveMessageCallback && this.receiveMessageCallback(JSON.parse(event.data))
  }

  /**
   * 关闭连接
   */
  closeWebSocket() {
    this.socket!.close()
    // 清除定时器 重置重连次数
    clearTimeout(this.pingTimeout)
  }
}
