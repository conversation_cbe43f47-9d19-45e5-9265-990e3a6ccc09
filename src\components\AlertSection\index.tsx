import { useState } from 'react'
import { useLocation } from 'react-router-dom'
import { Alert } from 'antd'
import { observer } from 'mobx-react-lite'
import { authStore } from '@/store'

const AlertSection = () => {
  const location = useLocation()
  const [close, setClose] = useState(false)
  const isShow =
    authStore.apikeyDosage < 3 &&
    ['/brandInsights/setting', '/brandInsights/keyword', '/brandInsights/account'].includes(
      location.pathname
    )

  return (
    isShow &&
    !close && (
      <Alert
        message="您的账户余额不足3元,为避免影响使用,请及时充值"
        type="warning"
        showIcon
        closable
        afterClose={() => setClose(true)}
        style={{
          margin: '0 14px'
        }}
      />
    )
  )
}

export default observer(AlertSection)
