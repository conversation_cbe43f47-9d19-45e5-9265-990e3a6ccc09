import React, { useCallback, useEffect, useState } from 'react'
import { Empty, message, Divider, Spin, Flex } from 'antd'
import WorkItemCard from '@/components/WorkItemCard'
import InfiniteScroll from 'react-infinite-scroll-component'
import { FilterBarValue } from './FilterBar'
import { getHotSaleList } from '@/services/hotSale'

const PAGE_SIZE = 40

// 定义数据类型 - 匹配 WorkItemCard 的接口要求
interface VideoItem {
  workId: string
  authorId: string
  authorName?: string
  avatar?: string
  location?: string
  followCount?: number
  uniqueId: string
  secUid: string
  url: string
  categoryType: number
  thumbnailLink: string
  isAd: number
  title: string
  content: string
  hashtags: string
  likeCount: number
  playCount: number
  commentCount: number
  shareCount: number
  videoUrl: string
  publishTime: number
}

interface CardListProps {
  filter: FilterBarValue
  onViewDetail?: (id: string) => void
}

const CardList: React.FC<CardListProps> = ({ filter }) => {
  const [page, setPage] = useState(1)
  const [loading, setLoading] = useState(false)
  const [works, setWorks] = useState<VideoItem[]>([])
  const [hasMore, setHasMore] = useState(true)

  // 构建 API 请求参数
  const buildApiParams = useCallback(
    (pageNo: number) => {
      const baseParams = {
        pageNo,
        pageSize: PAGE_SIZE,
        platform: filter.platform === 'all' ? undefined : filter.platform
      }

      // 处理时间筛选参数
      if (filter.time === 'all') {
        // 全部：不传时间参数
        return baseParams
      } else if (filter.customRange && filter.customRange[0] && filter.customRange[1]) {
        // 自定义日期范围：传开始和结束时间戳
        return {
          ...baseParams,
          startTime: filter.customRange[0].valueOf(), // 开始时间戳
          endTime: filter.customRange[1].valueOf() // 结束时间戳
        }
      } else if (filter.time !== 'custom') {
        // 具体天数：计算时间范围并转换为时间戳
        const now = new Date()
        const endTime = now.getTime()
        let startTime: number

        const daysMap: { [key: string]: number } = {
          '7d': 7,
          '14d': 14,
          '30d': 30
        }

        const days = daysMap[filter.time]
        if (days) {
          startTime = now.getTime() - days * 24 * 60 * 60 * 1000
          return {
            ...baseParams,
            startTime,
            endTime
          }
        }
      }

      // 默认返回基础参数
      return baseParams
    },
    [filter]
  )

  // 加载作品列表数据
  const fetchWorksList = useCallback(
    async (reset = false) => {
      if (loading) {
        return
      }

      const pageNo = reset ? 1 : page
      const params = buildApiParams(pageNo)

      setLoading(true)
      try {
        const res = await getHotSaleList(params)
        if (res.code === 'success') {
          const { data, totalCount, pageNo: servicePage, pageSize: servicePageSize } = res.result
          const apiHasMore = totalCount > servicePage * servicePageSize
          const newWorks = data || []

          // 根据是否重置来设置数据
          setWorks(prev => (reset ? newWorks : [...prev, ...newWorks]))
          setPage(servicePage + 1)
          setHasMore(apiHasMore && newWorks.length > 0)
        } else {
          message.error(res.msg || '加载失败')
        }
      } catch (error) {
        console.error('加载作品列表失败:', error)
        message.error('网络连接失败')
      } finally {
        setLoading(false)
      }
    },
    [loading, page, buildApiParams]
  )

  // 筛选条件变化时重新加载数据
  useEffect(() => {
    setWorks([])
    setHasMore(true)
    fetchWorksList(true)
  }, [filter])

  return (
    <div>
      {works.length === 0 && !loading ? (
        <div
          style={{
            padding: '40px 12px',
            background: '#fff',
            borderRadius: '8px'
          }}
        >
          <Empty description="暂无数据" />
        </div>
      ) : (
        <InfiniteScroll
          scrollableTarget="hotSale"
          dataLength={works.length}
          next={() => fetchWorksList(false)}
          hasMore={hasMore}
          loader={
            <Flex
              style={{ width: '100%', textAlign: 'center', padding: 16 }}
              align="center"
              justify="center"
            >
              <Spin size="small" />
              <span style={{ marginLeft: 8, color: '#888' }}>加载中...</span>
            </Flex>
          }
          endMessage={
            <div style={{ textAlign: 'center', padding: '16px', color: '#999' }}>
              <Divider plain>没有更多了~</Divider>
            </div>
          }
        >
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(240px, 1fr))',
              gap: 12,
              padding: 12,
              background: '#fff',
              borderRadius: 8
            }}
          >
            {works.map(item => (
              <div key={item.workId} style={{ minWidth: 240 }}>
                <WorkItemCard itemData={item} />
              </div>
            ))}
          </div>
        </InfiniteScroll>
      )}
    </div>
  )
}

export default CardList
