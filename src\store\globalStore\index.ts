import { makeAutoObservable } from 'mobx'
import { urlToBase64 } from '@/utils/file/base64Conver'
import cloudImg from '@/assets/imgae/cloud.png'

class GlobalStore {
  maskCloudImage = new Image()

  queryParams = {
    dateType: '7'
  }

  contentSectionDom?: HTMLElement = document.querySelector('#contentSection')!

  constructor() {
    makeAutoObservable(this)
  }

  getContentSectionDom() {
    this.contentSectionDom = document.querySelector('#contentSection')!
  }

  resetContentSectionDom() {
    this.contentSectionDom?.scrollTo(0, 0)
  }

  setQueryParams(newParams: any) {
    this.queryParams = { ...this.queryParams, ...newParams }
  }

  getMaskCloudImage() {
    urlToBase64(cloudImg).then(base64 => {
      this.maskCloudImage.src = base64
    })
  }
}

const globalStore = new GlobalStore()
export default globalStore
