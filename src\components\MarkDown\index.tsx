import React, { memo } from 'react'
import { default as ReactMarkdown } from 'react-markdown'
import './index.less'

interface MarkdownProps {
  content: string
  className?: string
}

const MarkDown: React.FC<MarkdownProps> = ({ content, className = '' }) => {
  if (!content) {
    return <div>暂无内容</div>
  }

  return (
    <div>
      <ReactMarkdown>{content}</ReactMarkdown>
    </div>
  )
}

export default memo(MarkDown)
