import React, { useState, useEffect, useRef } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Button, Flex, Image, Spin, Result } from 'antd'
import MarkDown from '../../MarkDown'
import {
  BulbFilled,
  LikeOutlined,
  LoadingOutlined,
  MessageOutlined,
  ReloadOutlined,
  ShareAltOutlined
} from '@ant-design/icons'
import { getAuthorTaskResult, addAuthorDisassembly } from '@/services/accountDetail'
import { accountDetailStore } from '@/store'
import { formatNumber, formatTimestamp } from '@/utils/delNumber'
import WorkDetial from '@/components/WorkDetial'
import { message } from '@/components/GlobalTips'
import { observer } from 'mobx-react-lite'
import styles from './AccountAnalysis.module.less'
import { isNumber } from 'lodash-es'

const { Text, Title } = Typography

const ContentSection = ({ content, workList }: { content: string; workList?: any[] }) => {
  const { atUniqueId } = accountDetailStore
  const [workId, setWorkId] = useState('')
  const [workDetailOpen, setWorkDetailOpen] = useState(false)

  return (
    <div className={styles.contentSection}>
      <MarkDown content={content} className={styles.markdownContent} />
      {workList && workList.length > 0 && (
        <Flex gap={12} style={{ marginTop: 12 }}>
          {workList.map(item => (
            <Flex
              key={item.id}
              className={styles.workCard}
              gap={12}
              onClick={() => {
                setWorkId(item.workId)
                setWorkDetailOpen(true)
              }}
            >
              <div>
                <Image
                  preview={false}
                  src={
                    item?.thumbnailLink ||
                    'https://linknext-**********.cos.ap-hongkong.myqcloud.com/material/1/********/images/5f93747218b2187c00bc83382dfff4e8.jpeg?imageMogr2/crop/300x400/gravity/center'
                  }
                  width={100}
                  height={100}
                  style={{ borderRadius: 12 }}
                />
              </div>
              <Flex vertical justify="space-between" style={{ flex: 1 }}>
                <div>
                  <Title
                    level={5}
                    style={{ margin: 0, fontSize: 14 }}
                    ellipsis={{ rows: 2, tooltip: true }}
                  >
                    {item?.title ||
                      '💧Summer’s dripping… and so are our nails!💅☀️ We printed this hyper-realistic water drop design and it’s giving fresh, juicy, just-dove-in-the-pool vibes 🏖️🌊 ✨ Created with AI, as always — Nailtitude is the first nail brand to use AI to design our press-ons 💭 Got a cool idea for a design? Drop it in the comments  #Nailtitude #SummerNails #WaterDropNails #FreshNailArt #PressOnNails #AInaildesign #YouSuggestWeCreate #NailTok #ViralNails #BlueVibes #GlossyMood'}
                  </Title>
                  <Text type="secondary" ellipsis>
                    发布：{formatTimestamp(item?.publishTime)}
                  </Text>
                </div>
                <Flex gap={12}>
                  <div>
                    <LikeOutlined />
                    <span style={{ marginLeft: 4 }}>{formatNumber(item?.likeCount)}</span>
                  </div>
                  <div>
                    <MessageOutlined />
                    <span style={{ marginLeft: 4 }}>{formatNumber(item?.commentCount)}</span>
                  </div>
                  <div>
                    <ShareAltOutlined />
                    <span style={{ marginLeft: 4 }}>{formatNumber(item?.shareCount)}</span>
                  </div>
                </Flex>
              </Flex>
            </Flex>
          ))}
        </Flex>
      )}
      {workDetailOpen && (
        <WorkDetial
          open={workDetailOpen}
          onClose={() => setWorkDetailOpen(false)}
          workId={workId}
          atUniqueId={atUniqueId}
        />
      )}
    </div>
  )
}

enum TaskStatus {
  Running = 0,
  Success = 1,
  Fail = 2,
  Init = 3
}

const AccountAnalysis: React.FC = () => {
  const { atUniqueId } = accountDetailStore
  const [taskStatus, setTaskStatus] = useState<TaskStatus>(TaskStatus.Init)
  const [updateTime, setUpdateTime] = useState(0)
  const [analysisData, setAnalysisData] = useState<{
    accountDiagnosis: string
    contentAnalysis: { content: string; relationWorks: any[] }[]
    hitVideoAnalysis: { content: string; relationWorks: any[] }[]
    hotScriptTemplates: { type: string; typeFeature: string; relationWorks: any[] }[]
    commercialAnalysis: string
  }>({
    accountDiagnosis: '',
    contentAnalysis: [],
    hitVideoAnalysis: [],
    hotScriptTemplates: [],
    commercialAnalysis: ''
  })
  const [loading, setLoading] = useState(true)
  const [updateLoading, setUpdateLoading] = useState(false)
  const timer = useRef<number | null>(null)

  const clearTimer = () => {
    if (timer.current) {
      clearTimeout(timer.current)
      timer.current = null
    }
  }

  const handleAddAuthorDisassembly = () => {
    setUpdateLoading(true)
    addAuthorDisassembly({ atUniqueId })
      .then(res => {
        if (res.code === 'success') {
          clearTimer()
          getTaskResult()
        } else {
          message.error(res.msg || '分析失败')
          setUpdateLoading(false)
        }
      })
      .catch(error => {
        console.error('添加账号分析失败:', error)
      })
  }

  const getTaskResult = () => {
    clearTimer()
    getAuthorTaskResult({ atUniqueId })
      .then(res => {
        if (res.code === 'success') {
          const { taskInfo, taskStatus: status = 3, updateTime: lasterTime } = { ...res.result }
          if (taskInfo && JSON.stringify(taskInfo) !== JSON.stringify(analysisData)) {
            setAnalysisData(taskInfo)
          }
          if (updateTime !== lasterTime) {
            setUpdateTime(lasterTime)
          }
          if (status !== taskStatus && isNumber(status)) {
            setTaskStatus(status)
          }
          setLoading(false)
          if (status === TaskStatus.Running && !timer.current) {
            timer.current = window.setTimeout(() => getTaskResult(), 10000)
          }
        } else {
          message.error(res.msg || '获取账号分析结果失败')
        }
      })
      .catch(error => {
        console.error('获取账号分析结果失败:', error)
      })
      .finally(() => {
        setUpdateLoading(false)
      })
  }

  useEffect(() => {
    if (
      accountDetailStore.activeTab === 'analysis' &&
      taskStatus === TaskStatus.Running &&
      !loading &&
      !timer.current
    ) {
      getTaskResult()
    } else {
      clearTimer()
    }
  }, [accountDetailStore.activeTab])

  useEffect(() => {
    getTaskResult()
    return () => clearTimer()
  }, [])

  if (loading) {
    return (
      <div style={{ textAlign: 'center', marginTop: 40 }}>
        <Spin />
      </div>
    )
  }

  return (
    <div className={styles.analysisContent}>
      {/* 账号拆解分析中 */}
      {taskStatus === TaskStatus.Running && (
        <Result
          icon={<LoadingOutlined />}
          title="账号分析中。。。"
          subTitle="预计1-3分钟请稍等..."
        />
      )}
      {/* 账号拆解分析报告 */}
      {taskStatus === TaskStatus.Success && (
        <div className={styles.analysisSection}>
          <div className={styles.sectionHeader}>
            <Title level={4} style={{ margin: 0 }}>
              账号拆解分析报告
            </Title>
            <Flex align="center">
              <span>更新时间： {formatTimestamp(updateTime)}</span>
              <Button
                loading={updateLoading}
                icon={<ReloadOutlined />}
                type="link"
                onClick={handleAddAuthorDisassembly}
              >
                更新
              </Button>
            </Flex>
          </div>

          {/* 01. 账号诊断 */}
          <div className={styles.analysisItem}>
            <div className={styles.analysisTitle}>
              <span className={styles.analysisNumber}>01.</span>
              <span className={styles.analysisLabel}>账号诊断</span>
            </div>
            <div className={styles.analysisContent}>
              <MarkDown
                content={analysisData.accountDiagnosis}
                className={styles.markdownContent}
              />
            </div>
          </div>
          {/* 02. 内容选题 */}
          <div className={styles.analysisItem}>
            <div className={styles.analysisTitle}>
              <span className={styles.analysisNumber}>02.</span>
              <span className={styles.analysisLabel}>内容选题</span>
            </div>
            <div className={styles.analysisContent}>
              {analysisData.contentAnalysis.map(item => (
                <ContentSection
                  key={item.content}
                  content={item.content}
                  workList={item.relationWorks}
                />
              ))}
            </div>
          </div>
          {/* 03. 爆款解析 */}
          <div className={styles.analysisItem}>
            <div className={styles.analysisTitle}>
              <span className={styles.analysisNumber}>03.</span>
              <span className={styles.analysisLabel}>爆款解析</span>
            </div>
            <div className={styles.analysisContent}>
              {analysisData.hitVideoAnalysis.map(item => (
                <ContentSection
                  key={item.content}
                  content={item.content}
                  workList={item.relationWorks}
                />
              ))}
            </div>
          </div>
          {/* 04. 爆款脚本模版 */}
          <div className={styles.analysisItem}>
            <div className={styles.analysisTitle}>
              <span className={styles.analysisNumber}>04.</span>
              <span className={styles.analysisLabel}>爆款脚本模版</span>
            </div>
            <div className={styles.analysisContent}>
              <Tabs
                className={styles.scriptTemplatesTabs}
                items={analysisData.hotScriptTemplates.map(template => ({
                  key: template.type,
                  label: template.type,
                  children: (
                    <div className={styles.templateTabContent}>
                      <ContentSection
                        content={template.typeFeature}
                        workList={template.relationWorks}
                      />
                    </div>
                  )
                }))}
                renderTabBar={(props, DefaultTabBar) => (
                  <DefaultTabBar {...props} className={styles.scriptTemplatesTabsBar} />
                )}
              />
            </div>
          </div>

          <div className={styles.analysisItem}>
            <div className={styles.analysisTitle}>
              <span className={styles.analysisNumber}>05.</span>
              <span className={styles.analysisLabel}>商业变现</span>
            </div>
            <div className={styles.analysisContent}>
              <MarkDown
                content={analysisData.commercialAnalysis}
                className={styles.markdownContent}
              />
            </div>
          </div>
        </div>
      )}
      {/* 账号拆解分析失败 */}
      {taskStatus === TaskStatus.Fail && (
        <Result
          status="error"
          title="生成失败"
          extra={
            <Button type="primary" onClick={handleAddAuthorDisassembly} loading={updateLoading}>
              重新分析
            </Button>
          }
        />
      )}
      {/* 账号拆解分析入口 */}
      {taskStatus === TaskStatus.Init && (
        <Flex className={styles.generateReport} gap={24}>
          <BulbFilled style={{ fontSize: '56px', color: '#ccc', paddingTop: 12 }} />
          <Flex vertical gap={12}>
            <div className={styles.reportTitle}>AI洞察账号 运营价值</div>
            <div className={styles.reportTitle}>账号选题分析，挖掘优质选题灵感</div>
            <div className={styles.reportTitle}>提取账号爆款模版，批量克隆同款脚本</div>
            <Button
              loading={updateLoading}
              type="primary"
              style={{ width: 200, marginTop: 24 }}
              onClick={handleAddAuthorDisassembly}
            >
              立即分析
            </Button>
          </Flex>
        </Flex>
      )}
    </div>
  )
}

export default observer(AccountAnalysis)
