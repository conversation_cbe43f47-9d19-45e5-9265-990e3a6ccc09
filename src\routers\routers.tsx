import { lazy } from 'react'
import { RouteType } from '.'
import NotFoundPage from '@/404'
import App from '@/App'
import ErrorPage from '@/ErrorPage'
import Login from '@/pages/Login'
import Person from '@/pages/Person'
import { Navigate } from 'react-router-dom'
import {
  HighlightFilled,
  IdcardFilled,
  ShakeOutlined,
  SmileFilled,
  UngroupOutlined
} from '@ant-design/icons'

export const routers = [
  {
    path: '/',
    /** 重定向 */
    element: <Navigate replace to="/accountTrace" />
  },
  {
    path: '/',
    /** 承载布局 */
    element: <App />,
    errorElement: <ErrorPage />,
    icon: <SmileFilled />,
    children: [
      /** 布局下路由，页面路由在该children配置 */
      {
        path: '/',
        name: '数据洞察',
        sort: 5,
        children: [
          {
            path: 'inspire',
            name: '作品采集',
            icon: <UngroupOutlined />,
            element: lazy(() => import('@/pages/Inspire'))
          },
          {
            path: 'hotSale',
            name: '爆款监控',
            icon: <HighlightFilled />,
            element: lazy(() => import('@/pages/HotSale'))
          },
          {
            path: 'accountTrace',
            name: '账号监控',
            icon: <ShakeOutlined />,
            element: lazy(() => import('@/pages/AccountTrace'))
          }
        ]
      },
      {
        id: 'person',
        path: 'person',
        name: '个人信息',
        sort: 100,
        icon: <IdcardFilled />,
        element: <Person />
      }
    ]
  },
  {
    path: '/login',
    name: '登录',
    element: <Login loginState="login" />
  },
  {
    path: '/register',
    name: '注册',
    element: <Login loginState="register" />
  },
  { path: '*', element: <NotFoundPage /> }
] as RouteType[]
