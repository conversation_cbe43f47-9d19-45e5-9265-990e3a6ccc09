import { useState } from 'react'
import { message } from 'antd'
import FilterBar, { FilterBarValue } from './components/FilterBar'
import InspireList from './components/InspireList'

const defaultFilter: FilterBarValue = {
  platform: 'all',
  workType: 'all',
  tag: 'all',
  time: 'all',
  customRange: null,
  searchKeyword: ''
}

const HotMonitor = () => {
  const [filter, setFilter] = useState<FilterBarValue>(defaultFilter)

  return (
    <>
      <div
        style={{
          background: '#fff',
          borderRadius: 8,
          height: 'calc(100vh - 68px)',
          overflow: 'auto'
        }}
      >
        <FilterBar value={filter} onChange={setFilter} />
        <InspireList filter={filter} />
      </div>
    </>
  )
}

const HotSale = () => {
  return (
    <>
      <div className="header" style={{ padding: '0 12px 12px' }}>
        <div className="left" style={{ fontSize: 18, fontWeight: 600, lineHeight: '32px' }}>
          作品采集
        </div>
      </div>
      <HotMonitor />
    </>
  )
}

export default HotSale
