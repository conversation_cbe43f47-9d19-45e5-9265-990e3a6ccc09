import React, { use<PERSON><PERSON>back, useEffect, useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Radio, Tabs, TabsProps } from 'antd'
import StickyBox from 'react-sticky-box'
import { DateType } from '@/constants'
import { globalStore } from '@/store'

import styles from './index.module.less'

const { Today, YesterDays, SevenDays, FifteenDays, ThirtyDays } = DateType
type DataTimeProps = {
  defaultValue?: DateType
  dataType?: DateType[]
}

const DataTime: React.FC<DataTimeProps> = props => {
  const defaultProps = {
    defaultValue: SevenDays,
    dataType: [SevenDays]
  }
  const { defaultValue, dataType } = { ...defaultProps, ...props }
  const [value, setValue] = useState(defaultValue)
  const radioList = [
    {
      value: Today,
      label: '昨天'
    },
    {
      value: YesterDays,
      label: '昨天'
    },
    {
      value: SevenDays,
      label: '7天'
    },
    {
      value: FifteenDays,
      label: '15天'
    },
    {
      value: ThirtyDays,
      label: '30天'
    }
  ].filter(item => dataType?.includes(item.value))
  useEffect(() => {
    setValue(defaultValue)
  }, [dataType])
  return (
    <div>
      <Radio.Group value={value} size="middle" className={styles.radioBtnList}>
        {radioList.map((item, index) => (
          <Radio.Button
            value={item.value}
            key={index}
            onChange={() => {
              globalStore.queryParams.dateType = item.value
              setValue(item.value)
            }}
          >
            {item.label}
          </Radio.Button>
        ))}
      </Radio.Group>
    </div>
  )
}

type PageTabSectionProps = TabsProps & {
  dataTimeProps?: DataTimeProps
}

const PageTabSection: React.FC<PageTabSectionProps> = ({
  dataTimeProps,
  defaultActiveKey,
  onChange,
  ...reset
}) => {
  const [loading, setLoading] = useState(true)
  const loacltion = useLocation()
  const navigate = useNavigate()
  useEffect(() => {
    globalStore.setQueryParams({ dateType: dataTimeProps?.defaultValue || SevenDays })
    setLoading(false)
    return () => {
      globalStore.setQueryParams({ dateType: SevenDays })
    }
  }, [])

  const handleKeywordChange = useCallback((key: string) => {
    globalStore.resetContentSectionDom()
    navigate(location.pathname, { state: { defaultValue: key } })
    onChange?.(key)
  }, [])
  if (loading) {
    return null
  }
  return (
    <Tabs
      className="lineTabs"
      size="small"
      onChange={handleKeywordChange}
      tabBarExtraContent={
        <div>
          <DataTime {...dataTimeProps} />
        </div>
      }
      renderTabBar={(props, DefaultTabBar) => (
        <StickyBox className="renderTabBar" offsetTop={0}>
          <DefaultTabBar {...props} />
        </StickyBox>
      )}
      defaultActiveKey={loacltion.state?.defaultValue || defaultActiveKey}
      {...reset}
    />
  )
}
export default PageTabSection
