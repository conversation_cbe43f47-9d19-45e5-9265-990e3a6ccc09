import { isNumber, isNull, isNaN, isString, isUndefined } from 'lodash-es'
import dayjs from 'dayjs'

// 格式化数字显示
export const formatNumber = (num: number | null | undefined): string => {
  if (isNull(num) || isUndefined(num) || isNaN(num)) {
    return '-'
  }
  if (num === 0) {
    return '0'
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

/**
 * 处理百分比
 */
export function formatPercentage(data: any, opt?: { toFixedNum?: number }) {
  const defaultOpt = { toFixedNum: 1 }
  const { toFixedNum = 2 } = { ...defaultOpt, ...opt }
  if (!data) {
    return '0%'
  }
  return `${(data * 100).toFixed(toFixedNum)}%`
}

/**
 * 处理空数据
 */
export function formatEmptyData(data: any, symbol = '-') {
  if (isNull(data) || isUndefined(data) || (isString(data) && !data)) {
    return symbol
  }
  return data
}

/**
 * 处理时间戳
 * @param timestamp 时间戳（毫秒或秒）或日期字符串
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的时间字符串，如果时间戳无效则返回 '-'
 */
export function formatTimestamp(
  timestamp: number | string | null | undefined,
  format: string = 'YYYY-MM-DD HH:mm:ss'
): string {
  // 如果时间戳为空或无效，返回 '-'
  if (isNull(timestamp) || isUndefined(timestamp) || timestamp === '' || timestamp === 0) {
    return '-'
  }

  try {
    let dayjsInstance

    if (isString(timestamp)) {
      // 如果是字符串，直接使用 dayjs 解析
      dayjsInstance = dayjs(timestamp)
    } else if (isNumber(timestamp)) {
      // 如果是数字，判断是秒还是毫秒
      // 一般来说，秒级时间戳长度为 10 位，毫秒级为 13 位
      if (timestamp.toString().length === 10) {
        // 秒级时间戳，转换为毫秒
        dayjsInstance = dayjs(timestamp * 1000)
      } else {
        // 毫秒级时间戳
        dayjsInstance = dayjs(timestamp)
      }
    } else {
      return '-'
    }

    // 检查日期是否有效
    if (!dayjsInstance.isValid()) {
      return '-'
    }

    return dayjsInstance.format(format)
  } catch (error) {
    // 如果解析出错，返回 '-'
    return '-'
  }
}

/**
 * 将秒数转换为时间格式（分钟:秒）
 * @param seconds 秒数
 * @returns 格式化后的时间字符串，格式为 "mm:ss"，如果秒数无效则返回 '-'
 */
export function formatSecondsToTime(seconds: number | null | undefined): string {
  // 如果秒数为空、无效或小于0，返回 '-'
  if (isNull(seconds) || isUndefined(seconds) || isNaN(seconds) || seconds < 0) {
    return '-'
  }

  try {
    // 将秒数转换为整数
    const totalSeconds = Math.floor(seconds)

    // 计算分钟、秒
    const minutes = Math.floor(totalSeconds / 60)
    const remainingSeconds = totalSeconds % 60

    // 格式化为两位数
    const formatTwoDigits = (num: number): string => num.toString().padStart(2, '0')

    return `${formatTwoDigits(minutes)}:${formatTwoDigits(remainingSeconds)}`
  } catch (error) {
    // 如果转换出错，返回 '-'
    return '-'
  }
}

/**
 * 计算时间差，返回几年几个月的格式
 * @param timestamp 时间戳（毫秒或秒）
 * @returns 时间差字符串，格式为"X年Y个月"，如果时间戳无效则返回 '-'
 */
export function formatTimeDifference(timestamp: number | null | undefined): string {
  // 如果时间戳为空、无效或为0，返回 '-'
  if (isNull(timestamp) || isUndefined(timestamp) || timestamp === 0) {
    return '-'
  }

  try {
    let targetTime: dayjs.Dayjs

    // 判断是秒还是毫秒级时间戳
    if (timestamp.toString().length === 10) {
      // 秒级时间戳，转换为毫秒
      targetTime = dayjs(timestamp * 1000)
    } else {
      // 毫秒级时间戳
      targetTime = dayjs(timestamp)
    }

    // 检查日期是否有效
    if (!targetTime.isValid()) {
      return '-'
    }

    const now = dayjs()

    // 计算年份差
    const yearDiff = now.year() - targetTime.year()

    // 计算月份差
    let monthDiff = now.month() - targetTime.month()

    // 如果当前日期小于目标日期的日，需要减去一个月
    if (now.date() < targetTime.date()) {
      monthDiff -= 1
    }

    // 处理月份为负数的情况
    let totalYears = yearDiff
    let totalMonths = monthDiff

    if (totalMonths < 0) {
      totalYears -= 1
      totalMonths += 12
    }

    // 格式化输出
    if (totalYears === 0 && totalMonths === 0) {
      return '不足1个月'
    } else if (totalYears === 0) {
      return `${totalMonths}个月`
    } else if (totalMonths === 0) {
      return `${totalYears}年`
    }
    return `${totalYears}年${totalMonths}个月`
  } catch (error) {
    // 如果计算出错，返回 '-'
    return '-'
  }
}
