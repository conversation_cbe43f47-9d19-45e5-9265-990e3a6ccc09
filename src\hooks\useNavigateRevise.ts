import { NavigateOptions, To, useLocation, useNavigate } from 'react-router-dom'
import NProgress from 'nprogress'

export default function useNavigateRevise() {
  const navigate = useNavigate()
  const location = useLocation()
  const _navigate = (to: To, options?: NavigateOptions) => {
    if (!NProgress.isStarted() && to !== location.pathname) {
      NProgress.start()
    }
    navigate(to, options)
  }
  return _navigate
}
