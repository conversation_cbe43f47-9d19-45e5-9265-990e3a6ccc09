import { authInfoStorage } from '@/utils/storage'
import { authStore } from '@/store'
import { isObject } from 'lodash-es'
import { Navigate } from 'react-router-dom'
import { message } from '@/components/GlobalTips'

const Permission: React.FC<{
  children: any
  name?: string
  permissionObj?: {
    isLoging?: boolean | undefined
    roles?: string
  } & boolean
}> = ({ children, permissionObj }) => {
  const token = authInfoStorage.get()?.token
  if (
    isObject(permissionObj) &&
    permissionObj.roles &&
    !authStore.roles.includes(permissionObj.roles)
  ) {
    return <Navigate replace to="/404" />
  }
  if ((permissionObj || permissionObj?.isLoging) && token) {
    return children
  }
  message.error('请重新登录')
  return <Navigate replace to={'/login'} />
}

export default Permission
