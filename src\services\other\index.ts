import axios from 'axios'
import { CORE } from '@/constants/service'
import http, { ResponseResult } from '@/utils/http'

/**
 * 添加跟进记录是上传附件
 */
export const uploadBatchUploadFileUrl = (
  data: {
    contentType?: string
    type: string
    extendMsg?: string
  }[]
): Promise<ResponseResult> => {
  return http.request({
    url: `${CORE}/upload/batchUploadFileUrl`,
    method: 'post',
    data
  })
}

/**
 * 文件上传oss
 */
export const uploadOssFile = (
  data: {
    url: string
    file: File
  }[]
) => {
  return Promise.all(
    data.map(({ url, file }) => {
      return axios.request({
        url: url,
        method: 'put',
        data: file,
        headers: {
          'Content-Type': file.type,
          timeout: 5000
        }
      })
    })
  )
}
