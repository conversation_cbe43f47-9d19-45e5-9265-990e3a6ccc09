// 全部作品内容
.worksContent {
  padding: 24px;
  padding-top: 0;

  .filterSection {
    padding: 20px 0;
    border-radius: 8px;
  }

  .filterRow {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    .filterSelectLabel {
      display: flex;
      gap: 12px;
      flex: 1;
      flex-wrap: nowrap;
      overflow-x: auto;
      white-space: nowrap;
    }
  }

  .worksGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
    padding: 16px 0;
    background: #fff;
    border-radius: 8px;
  }
}
