.accountDetail {
  flex: 1;
  width: 'auto';
  display: flex;
  flex-direction: column;
  overflow: auto;
}

// 顶部导航
.topNav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 100;
}

.backButton {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;

  &:hover {
    color: #1677ff;
  }
}

.pageTitle {
  margin: 0 0 0 16px;
  font-size: 18px;
  font-weight: 600;
}

// 标签页区域
.tabsContainer {
  flex: 1;
  margin: 0 24px;
  background: #fff;
  border-radius: 12px;
  border: 1px solid #e8e8e8;
}

.accountTabs {
  .accountTabsBar {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    margin: 0;
    padding: 0 24px;
  }
}
