import React from 'react'
import { But<PERSON> } from 'antd'
import { LinkOutlined } from '@ant-design/icons'
export const JumpLink: React.FC<{
  href: string
  showIcon?: boolean
  text?: string | React.ReactNode
}> = ({ href, text, showIcon }) => {
  return href ? (
    <Button
      type="link"
      target="_blank"
      rel="noreferrer"
      href={href}
      icon={showIcon && <LinkOutlined />}
    >
      {showIcon ? '' : text || '查看'}
    </Button>
  ) : (
    '-'
  )
}

export const LinkText: React.FC<{
  href: string
  text: string
}> = ({ href, text }) => {
  return href ? (
    <a title={text} className="linkText" href={href} target="_blank" rel="noreferrer">
      {text || '空'}
    </a>
  ) : (
    text || '-'
  )
}
