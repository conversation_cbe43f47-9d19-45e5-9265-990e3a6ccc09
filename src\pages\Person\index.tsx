import { useEffect, useState } from 'react'
import { authStore } from '@/store'
import {
  Button,
  Form,
  Space,
  Typography,
  Upload,
  Image,
  Input,
  Flex,
  Tooltip,
  Skeleton
} from 'antd'
import type { UploadFile, UploadProps } from 'antd'
import { ContentSection } from '@/components'
import { message } from '@/components/GlobalTips'
import { uploadBatchUploadFileUrl, uploadOssFile } from '@/services/other'
import { getAipToken, refreshAipToken } from '@/services/auth'
import { CopyFilled, EditTwoTone, ReloadOutlined } from '@ant-design/icons'
import { observer } from 'mobx-react-lite'
import ClipboardJS from 'clipboard'
const { Paragraph } = Typography

const EditUserImage = () => {
  const [fileList, setFileList] = useState<UploadFile[]>([
    {
      uid: '-1',
      name: 'user_avatar',
      url: authStore.userInfo.avatar
    }
  ])

  const onChange: UploadProps['onChange'] = ({ file }) => {
    const params = {
      contentType: file.type,
      type: 'user_avatar'
    }
    uploadBatchUploadFileUrl([params]).then(res => {
      if (res.code === 'success') {
        const { downloadUrl } = res.result[0]
        uploadOssFile([
          {
            url: res.result[0].uploadUrl,
            file: file as unknown as File
          }
        ]).then(async () => {
          await authStore.updateUserInfo({
            avatar: downloadUrl,
            nickname: authStore.userInfo.nickname
          })
          file.url = downloadUrl
          setFileList([file])
        })
      }
    })
  }

  return (
    <Space>
      <Image
        wrapperStyle={{
          overflow: 'hidden',
          borderRadius: '50%'
        }}
        width={80}
        height={80}
        src={fileList[0]?.url}
        fallback="data:image/png;base64,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"
      />
      <Upload
        action="/"
        accept=".png,.jpg,.jpeg,.svg"
        listType="text"
        fileList={fileList}
        onChange={onChange}
        beforeUpload={file => {
          if (!file.type.includes('image/')) {
            message.error('文件类型错误')
            return Upload.LIST_IGNORE
          }
          if (file.size > 2097152) {
            message.error('图片大小不能超过2M')
            return Upload.LIST_IGNORE
          }
          return false
        }}
        itemRender={() => {
          return null
        }}
      >
        <Button type="link">修改</Button>
      </Upload>
    </Space>
  )
}
const EditName = observer(() => {
  const [edit, setEdit] = useState(false)
  const [editableStr, setEditableStr] = useState(authStore.userInfo.nickname)
  const editUserName = () => {
    if (!editableStr) {
      message.error('昵称不能为空')
    } else {
      authStore
        .updateUserInfo({
          avatar: authStore.userInfo.avatar,
          nickname: editableStr
        })
        .then(() => {
          setEdit(false)
        })
    }
  }
  return (
    <div>
      {!edit ? (
        <Paragraph className="editParagraph" style={{ marginBottom: 0 }}>
          {authStore.userInfo.nickname}
          <EditTwoTone
            style={{ marginLeft: '4px', cursor: 'pointer' }}
            onClick={() => setEdit(true)}
          />
        </Paragraph>
      ) : (
        <Space>
          <Input
            showCount
            maxLength={20}
            autoFocus
            value={editableStr}
            onChange={e => setEditableStr(e.target.value)}
          />
          <Button type="primary" onClick={editUserName}>
            确定
          </Button>
          <Button onClick={() => setEdit(false)}>取消</Button>
        </Space>
      )}
    </div>
  )
})

const ApiKeys = () => {
  const [data, setData] = useState<any>()
  const [loading, setLoading] = useState(true)
  const getData = () => {
    setLoading(true)
    getAipToken().then(res => {
      if (res.code === 'success') {
        setData(res.result)
        setLoading(false)
      }
    })
  }

  const addApiKey = () => {
    setLoading(true)
    refreshAipToken().then(res => {
      if (res.code === 'success') {
        setData(res.result)
        setLoading(false)
        message.success('生成成功')
      }
    })
  }

  const copyText = () => {
    ClipboardJS.copy(data)
    message.success('复制成功')
  }

  useEffect(() => {
    getData()
  }, [])
  return (
    <Form.Item label="API Key">
      <Flex>
        {loading ? (
          <Skeleton.Input active size="default" style={{ width: 300 }} />
        ) : (
          <Input
            style={{ width: 300 }}
            value={
              data
                ? data.replace(/-(.*)(.{4})$/, (_: string, p1: string, p2: string) => {
                    return '-' + '*'.repeat(p1.length) + p2
                  })
                : ''
            }
          />
        )}
        <Tooltip title="生成ApiKey">
          <Button
            disabled={loading}
            type="link"
            icon={<ReloadOutlined style={{ fontSize: '18px' }} />}
            onClick={addApiKey}
          />
        </Tooltip>
        <Tooltip title="复制ApiKey">
          <Button
            disabled={!data}
            type="link"
            icon={<CopyFilled style={{ fontSize: '18px' }} />}
            onClick={copyText}
          />
        </Tooltip>
      </Flex>
    </Form.Item>
  )
}

const Person = () => {
  return (
    <ContentSection style={{ height: '100%' }} noMargin>
      <div style={{ height: '100%' }}>
        <Form layout="horizontal" initialValues={authStore.userInfo} labelCol={{ flex: '110px' }}>
          <div style={{ width: '50%' }}>
            <Form.Item label="头像">
              <EditUserImage />
            </Form.Item>
            <Form.Item label="昵称">
              <EditName />
            </Form.Item>
            {/* <Form.Item label="手机号码">{authStore.userInfo.phone}</Form.Item> */}
            {/* <Form.Item label="角色">{authStore.userInfo.roleName}</Form.Item> */}
            <Form.Item label="密码">
              <Button
                type="link"
                style={{ padding: 0 }}
                onClick={() => authStore.openUpdatePwdModule()}
              >
                修改密码
              </Button>
            </Form.Item>
            {/* <ApiKeys /> */}
            {/* <Form.Item label="API Key剩余量">
              <span style={{ color: '#1677ff' }}>{authStore.apikeyDosage}</span>
            </Form.Item> */}
          </div>
        </Form>
      </div>
    </ContentSection>
  )
}

export default Person
