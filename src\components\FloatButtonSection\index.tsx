import { useState } from 'react'
import { FloatButton, Image, Tooltip } from 'antd'
import { CustomerServiceOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons'
import { globalStore } from '@/store'
import { observer } from 'mobx-react-lite'
import erweima from '@/assets/imgae/erweima-dark.png'
function FloatButtonSection() {
  const [open, setOpen] = useState<boolean>(true)
  return (
    <FloatButton.Group
      open={open}
      onClick={() => setOpen(!open)}
      trigger="click"
      closeIcon={<RightOutlined />}
      icon={<LeftOutlined />}
      style={{ insetInlineEnd: 2 }}
    >
      <Tooltip
        color="white"
        placement="left"
        title={<Image src={''} width={180} height={180} preview={false} />}
      >
        <FloatButton icon={<CustomerServiceOutlined />} />
      </Tooltip>
      {globalStore.contentSectionDom && (
        <FloatButton.BackTop
          visibilityHeight={150}
          tooltip="回到顶部"
          target={() => globalStore.contentSectionDom as HTMLElement}
        />
      )}
    </FloatButton.Group>
  )
}

export default observer(FloatButtonSection)
