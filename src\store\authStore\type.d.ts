import { AuthStore as AuthStoreModel } from './index'

export as namespace IAuthStore

export type AuthStore = AuthStoreModel

export interface LoginParams {
  phone?: string
  email?: string
  messageCode?: string
  inviteCode?: string
  loginType: number
  password?: string
  type: number
}

export interface RegisterParams {
  email?: string
  messageCode?: string
  loginType: number
  password?: string
  type: number
}

export interface MenuTypes {
  permiCode: string
  permiName: string
  permiUrl: string
  permiRemark: string
  level: number
  showNum: number
}
