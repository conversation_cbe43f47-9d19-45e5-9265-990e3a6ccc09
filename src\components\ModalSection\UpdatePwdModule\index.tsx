import { useEffect, useState } from 'react'
import { observer } from 'mobx-react-lite'
import { authStore } from '@/store'
import { Form, Input, Modal } from 'antd'
import { message } from '@/components/GlobalTips'
import { updatePassword } from '@/services/auth'

const UpdatePasswordModule = () => {
  const [confirmLoading, setConfirmLoading] = useState(false)
  const [form] = Form.useForm()
  const closeModule = () => {
    authStore.closeUpdatePwdModule()
  }
  useEffect(() => {
    if (authStore.updatePwdVisible) {
      form.resetFields()
    }
  }, [authStore.updatePwdVisible])
  return (
    <Modal
      confirmLoading={confirmLoading}
      title="修改密码"
      open={authStore.updatePwdVisible}
      onClose={closeModule}
      onCancel={closeModule}
      onOk={() => {
        setConfirmLoading(true)
        form.validateFields().then(values => {
          updatePassword({
            password: values.password,
            phone: authStore.currentUserInfo.phone!
          })
            .then(res => {
              if (res.code === 'success') {
                closeModule()
                message.success('密码修改成功')
              } else {
                message.error(res.msg)
              }
            })
            .finally(() => {
              setConfirmLoading(false)
            })
        })
      }}
    >
      <Form form={form} labelCol={{ span: 4 }}>
        <Form.Item
          label="新密码"
          name="password"
          rules={[{ required: true, message: '请输入新密码' }]}
        >
          <Input.Password placeholder="请输入密码" autoComplete="off" />
        </Form.Item>
        <Form.Item
          label="确认密码"
          name="confirmPassword"
          dependencies={['password']}
          rules={[
            {
              required: true
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve()
                }
                return Promise.reject(new Error('密码不一致'))
              }
            })
          ]}
        >
          <Input.Password placeholder="请输入确认密码" />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default observer(UpdatePasswordModule)
