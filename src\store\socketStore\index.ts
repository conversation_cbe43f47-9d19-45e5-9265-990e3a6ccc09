import { makeAutoObservable } from 'mobx'

import { message as Message } from '@/components/GlobalTips'

import { SocketEventName } from './constans'

import { authInfoStorage } from '@/utils/storage'

import Ws from './ws'

export class SocketStore {
  socket: Ws

  constructor() {
    this.socket = new Ws({
      url: import.meta.env.VITE_SOCKET_URL!,
      receiveMessageCallback: this.onMessage
    })
    makeAutoObservable(this)
  }

  onMessage(message: ISocketStore.Message) {
    if (message.event === SocketEventName.AUTH_FAILED) {
      Message.error('请重新登录')
      authInfoStorage.remove()
    }
  }

  onStart() {
    this.socket.start()
  }

  onCancel() {
    this.socket.closeWebSocket()
  }
}

export default new SocketStore()
