import { useEffect } from 'react'
import { throttle } from 'lodash-es'

const useResizeEcharts = (
  echartRef: React.MutableRefObject<any>,
  containerRef: React.MutableRefObject<HTMLElement | null>
) => {
  useEffect(() => {
    const handleResize = throttle(() => {
      if (echartRef.current) {
        // 当容器尺寸变化时，手动调用 ECharts 的 resize 方法
        echartRef.current.getEchartsInstance().resize()
      }
    }, 300)

    // 创建 ResizeObserver 来监听容器尺寸变化
    const resizeObserver = new ResizeObserver(handleResize)

    const container = containerRef.current
    if (container) {
      resizeObserver.observe(container)
    }

    // 清理观察器
    return () => {
      if (container) {
        resizeObserver.unobserve(container)
      }
      handleResize.cancel()
    }
  }, [echartRef, containerRef]) // 加上依赖项，确保 ref 变化时重新设置
}

export default useResizeEcharts
