import React, { useState } from 'react'
import { Modal, Form, Select, Space, Input } from 'antd'
import { TikTokFilled } from '@ant-design/icons'
import { getAccountInfo } from '@/services/accountTrace'

const platformOptions = [
  {
    label: 'TikTok',
    value: 'tiktok',
    icon: <TikTokFilled />
  },
  {
    label: '抖音',
    value: 'douyin',
    icon: <TikTokFilled />
  }
]

interface AddAccountModalProps {
  open: boolean
  onCancel: () => void
  onOk: () => void
}

const CollectionModal: React.FC<AddAccountModalProps> = ({ open, onCancel, onOk }) => {
  const [form] = Form.useForm()
  const [submitLoading, setSubmitLoading] = useState(false)

  const handleFormSubmit = (values: any) => {
    setSubmitLoading(true)
    setTimeout(() => {
      getAccountInfo(values).then(res => {
        if (res.code === 'success') {
          setSubmitLoading(false)
          onOk()
        }
      })
    }, 1000)
  }

  return (
    <Modal
      open={open}
      title={<span style={{ fontWeight: 600, fontSize: 18 }}>添加监控账号</span>}
      onCancel={onCancel}
      onOk={() => form.submit()}
      okButtonProps={{
        loading: submitLoading
      }}
      okText="确定"
      cancelText="取消"
      destroyOnClose
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{ platform: 'tiktok', workUrl: '' }}
        onFinish={handleFormSubmit}
      >
        <Form.Item label="账号名">
          <Space.Compact style={{ width: '100%' }}>
            <Form.Item name="platform" style={{ width: 120 }}>
              <Select>
                {platformOptions.map(opt => (
                  <Select.Option
                    key={opt.value}
                    value={opt.value}
                    label={
                      <Space>
                        {opt.icon}
                        {opt.label}
                      </Space>
                    }
                  >
                    <Space>
                      {opt.icon}
                      {opt.label}
                    </Space>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              name="workUrl"
              style={{ flex: 1 }}
              rules={[{ required: true, message: '请输入作品链接' }]}
            >
              <Input placeholder="请输入作品链接" />
            </Form.Item>
          </Space.Compact>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default CollectionModal
