import React, { useState, useCallback, useMemo } from 'react'
import { Modal, Form, Select, Space, Spin, Avatar } from 'antd'
import { TikTokFilled } from '@ant-design/icons'
import { debounce } from 'lodash-es'
import { addAccountTrace, getAccountInfo } from '@/services/accountTrace'
import { message } from '@/components/GlobalTips'

const platformOptions = [
  {
    label: 'TikTok',
    value: 'tiktok',
    icon: <TikTokFilled />
  }
  // {
  //   label: '抖音',
  //   value: 'douyin',
  //   icon: <TikTokFilled />
  // }
]

interface AddAccountModalProps {
  open: boolean
  onCancel: () => void
  onOk: () => void
}

const AddAccountModal: React.FC<AddAccountModalProps> = ({ open, onCancel, onOk }) => {
  const [form] = Form.useForm()
  const [platform, setPlatform] = useState('tiktok')
  const [submitLoading, setSubmitLoading] = useState(false)
  const [searchOptions, setSearchOptions] = useState<any[]>([])
  const [searchLoading, setSearchLoading] = useState(false)
  const [searchValue, setSearchValue] = useState('')

  // 搜索账号的函数
  const searchAccounts = useCallback(async (searchText: string, currentPlatform: string) => {
    if (!searchText.trim()) {
      setSearchOptions([])
      return
    }

    setSearchLoading(true)
    try {
      const res = await getAccountInfo({
        platform: currentPlatform,
        search: searchText
      })
      if (res.code === 'success' && res.result) {
        setSearchOptions([{ ...res.result }])
      } else {
        message.error(res.msg || '搜索失败')
        setSearchOptions([])
      }
    } catch (error) {
      console.error('搜索账号失败:', error)
      setSearchOptions([])
    } finally {
      setSearchLoading(false)
    }
  }, [])

  // 使用 debounce 优化搜索
  const debouncedSearch = useMemo(
    () =>
      debounce((searchText: string, currentPlatform: string) => {
        searchAccounts(searchText, currentPlatform)
      }, 500),
    [searchAccounts]
  )

  // 处理搜索输入
  const handleSearch = useCallback(
    (value: string) => {
      setSearchValue(value)
      debouncedSearch(value, platform)
    },
    [debouncedSearch, platform]
  )

  // 处理平台切换
  const handlePlatformChange = useCallback(
    (newPlatform: string) => {
      setPlatform(newPlatform)
      setSearchOptions([])
      setSearchValue('')
      form.setFieldValue('authId', undefined)
    },
    [form, searchValue, debouncedSearch]
  )

  const handleFormSubmit = (values: any) => {
    setSubmitLoading(true)
    addAccountTrace({ atUniqueId: values?.uniqueId })
      .then(res => {
        if (res.code === 'success') {
          setSubmitLoading(false)
          onOk()
        }
      })
      .catch(() => {
        setSubmitLoading(false)
      })
  }

  return (
    <Modal
      open={open}
      title={<span style={{ fontWeight: 600, fontSize: 18 }}>添加监控账号</span>}
      onCancel={onCancel}
      onOk={() => form.submit()}
      okButtonProps={{
        loading: submitLoading
      }}
      okText="确定"
      cancelText="取消"
      destroyOnClose
      width={600}
    >
      <Form form={form} layout="vertical" initialValues={{ platform }} onFinish={handleFormSubmit}>
        <Form.Item label="账号名">
          <Space.Compact style={{ width: '100%' }}>
            <Form.Item name="platform" style={{ width: 120 }}>
              <Select onChange={handlePlatformChange}>
                {platformOptions.map(opt => (
                  <Select.Option
                    key={opt.value}
                    value={opt.value}
                    label={
                      <Space>
                        {opt.icon}
                        {opt.label}
                      </Space>
                    }
                  >
                    <Space>
                      {opt.icon}
                      {opt.label}
                    </Space>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              name="uniqueId"
              style={{ flex: 1 }}
              rules={[{ required: true, message: '请选择或输入作者主页链接' }]}
            >
              <Select
                showSearch
                placeholder="请输入账号主页链接"
                filterOption={false}
                onSearch={handleSearch}
                loading={searchLoading}
                notFoundContent={searchLoading ? <Spin size="small" /> : '暂无数据'}
                allowClear
                searchValue={searchValue}
                onClear={() => {
                  setSearchValue('')
                  setSearchOptions([])
                }}
              >
                {searchOptions.map(option => (
                  <Select.Option
                    key={option.uniqueId}
                    value={option.uniqueId}
                    onclick={() => {
                      form.setFieldValue('uniqueId', option.uniqueId)
                    }}
                  >
                    <Space align="center">
                      <Avatar
                        src={option.authorAvatar}
                        size={20}
                        style={{ verticalAlign: 'center', marginBottom: 3 }}
                      />
                      <span>{option.authorName}</span>
                    </Space>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Space.Compact>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default AddAccountModal
