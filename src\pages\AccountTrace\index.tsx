import { useState } from 'react'
import AccountFilterBar, { AccountFilterBarValue } from './components/AccountFilterBar'
import AccountTable from './components/AccountTable'

const AccountList = () => {
  const [filter, setFilter] = useState<AccountFilterBarValue>({ platform: 'all', search: '' })

  return (
    <div
      style={{
        background: '#fff',
        borderRadius: 8,
        overflow: 'auto',
        height: 'calc(100vh - 68px)'
      }}
    >
      <AccountFilterBar value={filter} onChange={setFilter} />
      <AccountTable filter={filter} />
    </div>
  )
}

const AccountTrace = () => {
  return (
    <>
      <div className="header" style={{ padding: '0 12px 12px' }}>
        <div className="left" style={{ fontSize: 18, fontWeight: 600, lineHeight: '32px' }}>
          账号监控
        </div>
      </div>
      <AccountList />
    </>
  )
}

export default AccountTrace
