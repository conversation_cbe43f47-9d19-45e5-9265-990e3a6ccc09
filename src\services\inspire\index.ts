import { CORE } from '@/constants/service'
import http, { ResponseResult } from '@/utils/http'
import { v4 as uuidv4 } from 'uuid'

const addInspire = (params: { platform: string; url: string }) => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 'success',
        msg: '',
        result: {}
      })
    }, 500)
  })
}

const getInspireList = (params: {
  pageNo: number
  pageSize: number
  days?: string
  startTime?: string
  endTime?: string
  platform?: string
  search?: string
}): Promise<ResponseResult<any>> => {
  const { platform, pageNo, pageSize } = params
  console.log('params', params)

  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        code: 'success',
        msg: '',
        result: {
          pageNo,
          pageSize,
          data: Array.from({ length: pageSize }, (_, i) => ({
            id: uuidv4(),
            title: '视频标题 平台',
            authorName: '用户',
            location: platform === 'tiktok' ? '美国' : '中国',
            likeCount: String(Math.floor(Math.random() * 10000)),
            commentCount: String(Math.floor(Math.random() * 1000)),
            shareCount: String(Math.floor(Math.random() * 500)),
            followCount: `${(Math.random() * 10).toFixed(1)}M`,
            duration: `${Math.floor(Math.random() * 60)}s`
          })),
          hasMore: true,
          total: 100000
        }
      })
    }, 500)
  })
}

export { addInspire, getInspireList }
