import { makeAutoObservable, runInAction } from 'mobx'
import {
  getAccountDetail,
  refreshAccountDetail as refreshAccountDetailService
} from '@/services/accountDetail'
import type { AccountDetail } from './type'
import { message } from '@/components/GlobalTips'

class AccountDetailStore {
  refrefreshLoading = false

  refresh = 0

  loading = false

  atUniqueId = ''

  accountDetailData: AccountDetail | null = null

  activeTab = 'overview'
  error: string | null = null

  constructor() {
    makeAutoObservable(this)
  }

  // 设置加载状态
  setLoading(loading: boolean) {
    this.loading = loading
  }

  // 设置错误信息
  setError(error: string | null) {
    this.error = error
  }

  // 设置当前激活的标签页
  setActiveTab(tab: string) {
    this.activeTab = tab
  }

  // 设置详情
  setAccountDetail(detail: AccountDetail | null) {
    this.accountDetailData = detail
  }

  // 设置详情
  setIncreaseChartData(detail: any | null) {
    this.accountDetailData = detail
  }

  // 首次进入获取作品详情
  async fetchAccountDetail(noLoading = false) {
    if (!noLoading) {
      noLoading || this.setLoading(true)
    }
    try {
      this.setError(null)
      const detailRes = await getAccountDetail({ atUniqueId: this.atUniqueId })

      runInAction(() => {
        if (detailRes.code === 'success') {
          this.setAccountDetail({
            ...detailRes.result.accountInfo,
            today: detailRes.result.today,
            yesterday: detailRes.result.yesterday
          })
        }
      })
    } catch (error) {
      runInAction(() => {
        this.setError(error instanceof Error ? error.message : '获取详情失败')
      })
    } finally {
      runInAction(() => {
        this.setLoading(false)
      })
    }
  }

  // 手动刷新
  refreshAccountDetail() {
    this.refrefreshLoading = true
    try {
      refreshAccountDetailService({ atUniqueId: this.atUniqueId }).then(res => {
        message.success('更新成功')
        this.refresh++
        this.fetchAccountDetail(true)
      })
    } catch (error) {
      runInAction(() => {
        this.setError(error instanceof Error ? error.message : '刷新失败')
      })
    } finally {
      runInAction(() => {
        this.refrefreshLoading = false
      })
    }
  }

  // 重置store状态
  reset() {
    this.refresh = 0
    this.loading = false
    this.atUniqueId = ''
    this.accountDetailData = null
    this.activeTab = 'overview'
    this.error = null
    this.refrefreshLoading = false
  }
}

const accountDetailStore = new AccountDetailStore()

export { AccountDetailStore }
export default accountDetailStore
