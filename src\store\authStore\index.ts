import { message } from '@/components/GlobalTips'
import {
  getCurrentUserInfo,
  loginService,
  loginOutService,
  updateUserInfoService,
  getApipTokenQuota,
  registerService
} from '@/services/auth'
import { authInfoStorage } from '@/utils/storage'
import { makeAutoObservable, runInAction } from 'mobx'

class AuthStore {
  userInfo: Partial<Auth.AuthInfo> = {}

  menuRoles: IAuthStore.MenuTypes[] = []

  apikeyDosage = 0

  updatePwdVisible = false

  constructor() {
    makeAutoObservable(this)
  }

  async login(params: IAuthStore.LoginParams) {
    try {
      const data = await loginService(params)
      runInAction(() => {
        this.userInfo = data.result
      })
      authInfoStorage.set(this.userInfo)
      return Promise.resolve(true)
    } catch (error: any) {
      return Promise.reject(error)
    }
  }

  async register(params: IAuthStore.RegisterParams) {
    try {
      await registerService(params)
      return Promise.resolve(true)
    } catch (error: any) {
      return Promise.reject(error)
    }
  }

  async updateUserInfo(params: { avatar?: string; nickname?: string }) {
    updateUserInfoService(params).then(res => {
      if (res.code === 'success') {
        message.success('修改成功')
        runInAction(() => {
          this.userInfo = { ...this.userInfo, ...params }
        })
        const localInfo = authInfoStorage.get()
        authInfoStorage.set({ ...localInfo, ...params })
        return Promise.resolve()
      }
      message.error(res.msg)
      return Promise.reject()
    })
  }

  async logout() {
    try {
      await loginOutService()
      authInfoStorage.remove()
      return Promise.resolve(true)
    } catch (error) {
      return Promise.reject()
    }
  }

  async getUserDetail() {
    try {
      const [
        userInfo
        // apikeyDosage
      ] = await Promise.all([
        getCurrentUserInfo()
        // getApipTokenQuota()
      ])
      const { result } = userInfo
      // result.userPhone = `${result.phone.substr(0, 3)}****${result.phone.slice(-4)}`
      runInAction(() => {
        this.userInfo = result
        // this.apikeyDosage = apikeyDosage.result
      })
      const localInfo = authInfoStorage.get()
      authInfoStorage.set({ ...localInfo, ...result })
      return Promise.resolve(true)
    } catch (error) {
      return Promise.resolve(false)
    }
  }

  // apiKey剩余量
  async getApikeyDosage() {
    try {
      const { result } = await getApipTokenQuota()
      runInAction(() => {
        this.apikeyDosage = result
      })
      return Promise.resolve(true)
    } catch (error) {
      return Promise.resolve(false)
    }
  }

  setUserInfo(user: Partial<Auth.AuthInfo>) {
    this.userInfo = user
  }

  get currentUserInfo(): Auth.AuthInfo {
    return authInfoStorage.get() || {}
  }

  get roles(): string[] {
    return this.menuRoles.map((item: any) => item.permiCode) || []
  }

  closeUpdatePwdModule() {
    runInAction(() => {
      this.updatePwdVisible = false
    })
  }
  openUpdatePwdModule() {
    runInAction(() => {
      this.updatePwdVisible = true
    })
  }
}

const authStore = new AuthStore()
export default authStore
