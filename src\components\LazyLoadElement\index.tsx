import { observer } from 'mobx-react-lite'
import { globalStore } from '@/store'
import LazyLoad, { LazyLoadProps } from 'react-lazyload'

const LazyLoadElement = (props: LazyLoadProps) => {
  return (
    <LazyLoad
      height={300}
      once
      scrollContainer={globalStore.contentSectionDom!}
      offset={50}
      overflow
      {...props}
    >
      {props.children}
    </LazyLoad>
  )
}
export default observer(LazyLoadElement)
