import http, { ResponseResult } from '@/utils/http'

const getWorkDetail = (params: {
  workId: string
  atUniqueId?: string
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/article/info',
    method: 'get',
    params
  })
}

const getWorkDetailComment = (
  params: {
    workId: string
    atUniqueId?: string
  } & Global.PageParams
): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/article/comment/list',
    method: 'get',
    params
  })
}

const refreshWorkDetail = (params: {
  workId: string
  atUniqueId?: string
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/article/sync',
    method: 'get',
    params
  })
}

const getWorkCommentStatus = (params: {
  workId: string
  atUniqueId?: string
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/article/comment/updateStatus',
    method: 'get',
    params
  })
}

const refreshWorkComment = (params: {
  workId: string
  atUniqueId?: string
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/article/comment/sync',
    method: 'get',
    params
  })
}

// 获取作品视频结果
const getWorkQuotesTaskResult = (data: {
  atUniqueId?: string
  workId: string
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/ai/action/queryWorkTaskResult',
    method: 'post',
    data
  })
}

// 添加作品视频分析
const addWorkQuotesDisassembly = (data: {
  atUniqueId?: string
  workId: string
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/ai/action/addTranscriptsTask',
    method: 'post',
    data
  })
}

// 获取作品视频解构结果
const getWorkVideoTaskResult = (params: {
  atUniqueId?: string
  workId: string
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/ai/action/queryAuthorTaskResult1',
    method: 'get',
    params
  })
}

// 添加作品视频解构
const addWorkVideoDisassembly = (params: {
  atUniqueId?: string
  workId: string
}): Promise<ResponseResult<any>> => {
  return http.request({
    url: '/ai/action/addAuthorDisassembly1',
    method: 'get',
    params
  })
}

export {
  getWorkDetail,
  getWorkDetailComment,
  refreshWorkDetail,
  refreshWorkComment,
  getWorkCommentStatus,
  getWorkQuotesTaskResult,
  addWorkQuotesDisassembly,
  getWorkVideoTaskResult,
  addWorkVideoDisassembly
}
