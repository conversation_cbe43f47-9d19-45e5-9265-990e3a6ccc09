import { Base64 } from 'js-base64'
import { enc, mode, AES, pad } from 'crypto-js'

export const AESEncrypt = (plainText: string, key: string, iv: string) => {
  if (!plainText) {
    return ''
  }
  // 加密数据
  const base64Key = enc.Base64.parse(Base64.toBase64(key))
  const result = AES.encrypt(plainText, base64Key, {
    iv: enc.Base64.parse(Base64.toBase64(iv)),
    mode: mode.CFB,
    padding: pad.Pkcs7
  }).toString()

  return result
}

export const AESDecrypt = (ciphertext: string, key: string, iv: string) => {
  if (!ciphertext) {
    return ''
  }
  const base64Key = enc.Base64.parse(Base64.toBase64(key))
  const result = AES.decrypt(ciphertext, base64Key, {
    iv: enc.Base64.parse(Base64.toBase64(iv)),
    mode: mode.CFB,
    padding: pad.Pkcs7
  }).toString(enc.Utf8)
  return result
}

export const AESEncryptPwd = (pwd: string) => {
  return AESEncrypt(pwd, '83c933c49a5179dd1a8b9d6111111111', '3kf93mg02ga11111')
}

export const AESDecryptPwd = (pwd: string) => {
  return AESDecrypt(pwd, '83c933c49a5179dd1a8b9d6111111111', '3kf93mg02ga11111')
}
