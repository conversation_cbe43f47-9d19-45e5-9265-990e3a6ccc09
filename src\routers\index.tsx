import Permission from '@/components/Permission'
import { MenuDataItem } from '@ant-design/pro-components'
import { createBrowserRouter, Outlet, RouteObject } from 'react-router-dom'
import { routers } from './routers'

export type RouteType = {
  /** 是否隐藏菜单布局 */
  hideLayout?: boolean
  /** 在菜单栏是否显示 */
  hideInMenu?: boolean
  /** 权限控制 true 则都控制 */
  permissionObj?: {
    /** 判断token是否存在控制 */
    isLoging?: boolean
    /** 页面权限code */
    roles?: string
  } & true
  outlet?: boolean
  redirect?: string
  children?: RouteType[]
  element?: any
  sort?: number
} & Partial<MenuDataItem> &
  RouteObject

/** 判断是否是 lazy 组件 */
const isLazyComponent = (component: any) => {
  return component.$$typeof === Symbol.for('react.lazy')
}
const renderElement = (item: RouteType) => {
  const { element: Element, permissionObj, name } = item
  if (Element) {
    const component = isLazyComponent(Element) ? <Element /> : Element
    if (permissionObj) {
      return (
        <Permission name={name} permissionObj={permissionObj}>
          {component}
        </Permission>
      )
    }
    return component
  }
  return permissionObj ? (
    <Permission name={name} permissionObj={permissionObj}>
      <Outlet />
    </Permission>
  ) : (
    <Outlet />
  )
}

const reduceRoute: (params: RouteType[], parentPath?: string) => RouteType[] = (
  routesParams: RouteType[]
) => {
  return routesParams?.map(item => {
    let curRouter = item
    curRouter.element = renderElement(item)
    if (item?.children) {
      curRouter = {
        ...curRouter,
        children: reduceRoute(item?.children) as any
      }
    }
    return curRouter
  })
}

const relRouters = reduceRoute(routers)

const router = createBrowserRouter(relRouters, {
  future: {
    v7_fetcherPersist: true,
    v7_normalizeFormMethod: true,
    v7_partialHydration: true,
    v7_relativeSplatPath: true,
    v7_skipActionErrorRevalidation: true
  }
})

export { router, routers }
