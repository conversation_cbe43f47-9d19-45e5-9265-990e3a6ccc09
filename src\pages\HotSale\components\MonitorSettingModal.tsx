import React, { useEffect, useState } from 'react'
import { Modal, Form, Row, Col, Typography, InputNumber } from 'antd'
import { getHotSaleSetting, setHotSaleSetting } from '@/services/hotSale'
import { message } from '@/components/GlobalTips'

const { Text } = Typography

interface MonitorSettingModalProps {
  open: boolean
  onCancel: () => void
  onOk?: (values: any) => void
}

const MonitorSettingModal: React.FC<MonitorSettingModalProps> = ({ open, onCancel, onOk }) => {
  const [form] = Form.useForm()
  const [modalLoading, setModalLoading] = useState(true)
  const [loading, setLoading] = useState(false)
  const [initialValues, setInitialValues] = useState<any>({})

  const handleSubmit = (values: any) => {
    setLoading(true)
    setHotSaleSetting(values).then((res: any) => {
      if (res.code === 'success') {
        onCancel()
        message.success('监控设置已保存')
      } else {
        setLoading(false)
      }
    })
  }

  useEffect(() => {
    if (open) {
      setModalLoading(true)
      getHotSaleSetting().then((res: any) => {
        setInitialValues(res.result)
        setModalLoading(false)
      })
    }
  }, [open])

  return (
    <Modal
      loading={modalLoading}
      open={open}
      title={<span style={{ fontWeight: 600, fontSize: 18 }}>监控设置</span>}
      onCancel={onCancel}
      onOk={() => form.submit()}
      okText="确定"
      cancelText="取消"
      okButtonProps={{ loading }}
      destroyOnClose
      width={520}
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit} initialValues={initialValues}>
        <div style={{ fontWeight: 500, marginBottom: 16 }}>
          爆款标准设置：
          <Text type="secondary" style={{ fontWeight: 400, marginLeft: 8 }}>
            满足以下任一条件即为爆款
          </Text>
        </div>

        {/* 播放数 */}
        <Row align="middle" gutter={16}>
          <Col span={6} style={{ paddingBottom: 24 }}>
            <Text>播放数：达到</Text>
          </Col>
          <Col span={8}>
            <Form.Item name="playCount" rules={[{ required: true, message: '请输入播放数' }]}>
              <InputNumber style={{ width: '100%' }} placeholder="请输入播放数" />
            </Form.Item>
          </Col>
          <Col span={4} style={{ paddingBottom: 24 }}>
            <Text>即为爆款</Text>
          </Col>
        </Row>

        {/* 点赞数 */}
        <Row align="middle" gutter={16}>
          <Col span={6} style={{ paddingBottom: 24 }}>
            <Text>点赞数：达到</Text>
          </Col>
          <Col span={8}>
            <Form.Item name="likeCount" rules={[{ required: true, message: '请输入点赞数' }]}>
              <InputNumber style={{ width: '100%' }} placeholder="请输入点赞数" />
            </Form.Item>
          </Col>
          <Col span={4} style={{ paddingBottom: 24 }}>
            <Text>即为爆款</Text>
          </Col>
        </Row>

        {/* 评论数 */}
        <Row align="middle" gutter={16}>
          <Col span={6} style={{ paddingBottom: 24 }}>
            <Text>评论数：达到</Text>
          </Col>
          <Col span={8}>
            <Form.Item name="commentCount" rules={[{ required: true, message: '请输入评论数' }]}>
              <InputNumber style={{ width: '100%' }} placeholder="请输入评论数" />
            </Form.Item>
          </Col>
          <Col span={4} style={{ paddingBottom: 24 }}>
            <Text>即为爆款</Text>
          </Col>
        </Row>

        {/* 转发数 */}
        <Row align="middle" gutter={16}>
          <Col span={6} style={{ paddingBottom: 24 }}>
            <Text>转发数：达到</Text>
          </Col>
          <Col span={8}>
            <Form.Item name="shareCount" rules={[{ required: true, message: '请输入转发数' }]}>
              <InputNumber style={{ width: '100%' }} placeholder="请输入转发数" />
            </Form.Item>
          </Col>
          <Col span={4} style={{ paddingBottom: 24 }}>
            <Text>即为爆款</Text>
          </Col>
        </Row>

        {/* 收藏数 */}
        <Row align="middle" gutter={16}>
          <Col span={6} style={{ paddingBottom: 24 }}>
            <Text>收藏数：达到</Text>
          </Col>
          <Col span={8}>
            <Form.Item name="collectCount" rules={[{ required: true, message: '请输入收藏数' }]}>
              <InputNumber style={{ width: '100%' }} placeholder="请输入收藏数" />
            </Form.Item>
          </Col>
          <Col span={4} style={{ paddingBottom: 24 }}>
            <Text>即为爆款</Text>
          </Col>
        </Row>

        <div style={{ fontWeight: 500, marginBottom: 16 }}>粉丝暴涨标准设置</div>

        {/* 粉丝增量 */}
        <Row align="middle" gutter={16}>
          <Col span={8} style={{ paddingBottom: 24 }}>
            <Text>账号粉丝当日增量达到</Text>
          </Col>
          <Col span={6}>
            <Form.Item name="fansIncrease" rules={[{ required: true, message: '请输入粉丝增量' }]}>
              <InputNumber style={{ width: '100%' }} placeholder="请输入粉丝增量" />
            </Form.Item>
          </Col>
          <Col span={4} style={{ paddingBottom: 24 }}>
            <Text>即为暴涨</Text>
          </Col>
        </Row>
      </Form>
    </Modal>
  )
}

export default MonitorSettingModal
