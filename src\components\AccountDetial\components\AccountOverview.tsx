import { useEffect, useMemo, useState } from 'react'
import { Typo<PERSON>, Select, Button, DatePicker, Progress, Flex } from 'antd'
import { EChartPrivider } from '@/components'
import { getFollowerTrendOption, getPublishFrequencyOption } from '@/charts'
import dayjs from 'dayjs'
import styles from './AccountOverview.module.less'
import { observer } from 'mobx-react-lite'
import { accountDetailStore } from '@/store'
import { getIncreaseChart, getPublishFrequency, getTopWorksLabel } from '@/services/accountDetail'
import { formatTimestamp, formatNumber } from '@/utils/delNumber'

const { Text, Paragraph, Title } = Typography
const { Option } = Select

// 增长趋势区域
const increaseMetricOptions = [
  { value: 'addFansCount', label: '新增粉丝数' },
  { value: 'addLikeCount', label: '新增点赞数' },
  { value: 'addPlayCount', label: '新增评论数' },
  { value: 'addShareCount', label: '新增转发数' },
  { value: 'addCollectCount', label: '新增收藏数' }
]

const increasePeriodOptions = [
  { value: '7', label: '近7天' },
  { value: '15', label: '近14天' },
  { value: '30', label: '近30天' }
]
const IncreaseChartSection = () => {
  const { atUniqueId } = accountDetailStore
  const [chartData, setChartData] = useState<any>(null)
  const [chartLoading, setChartLoading] = useState(true)
  const [selectedMetric, setSelectedMetric] = useState('addFansCount')
  const [selectedPeriod, setSelectedPeriod] = useState('7')
  const alertText = useMemo(() => {
    const metric = increaseMetricOptions.find(opt => opt.value === selectedMetric)?.label
    const period = increasePeriodOptions.find(opt => opt.value === selectedPeriod)?.label
    const total = formatNumber(
      chartData?.statItem.reduce((acc: number, cur: any) => acc + cur[selectedMetric], 0)
    )
    const max =
      chartData &&
      formatNumber(Math.max(...chartData.statItem.map((item: any) => item[selectedMetric])))
    return (
      <>
        <span>{period}</span> {metric} <span>{total}</span>，当日最高{metric}
        <span>{max}</span>
      </>
    )
  }, [selectedMetric, selectedPeriod, chartLoading])

  const getIncreaseChartData = () => {
    setChartLoading(true)
    getIncreaseChart({
      atUniqueId,
      datType: selectedPeriod
    })
      .then(res => {
        if (res.code === 'success') {
          setChartData(res.result)
        }
      })
      .finally(() => {
        setChartLoading(false)
      })
  }

  useEffect(() => {
    getIncreaseChartData()
  }, [selectedPeriod])

  return (
    <div className={styles.chartSection}>
      <div className={styles.sectionHeader}>
        <Title level={5} className={styles.borderTitle}>
          增长趋势
        </Title>
        <div className={styles.chartControls}>
          <Select
            value={selectedMetric}
            onChange={setSelectedMetric}
            style={{ width: 120, marginRight: '12px' }}
          >
            {increaseMetricOptions.map(opt => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
          <Select value={selectedPeriod} onChange={setSelectedPeriod} style={{ width: 100 }}>
            {increasePeriodOptions.map(opt => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        </div>
      </div>

      <div className={styles.trendInfo}>{alertText}</div>

      <div className={styles.chartContainer}>
        <EChartPrivider
          chartLoading={chartLoading}
          data={chartData}
          option={getFollowerTrendOption({
            label: increaseMetricOptions.find(opt => opt.value === selectedMetric)!.label,
            dateTime: chartData?.dateTime,
            statItem: chartData?.statItem.map((item: any) => item[selectedMetric])
          })}
        />
      </div>
    </div>
  )
}

// 发布频率
const publishFrequencyTypeOptions = [
  {
    value: 'day',
    label: '按日'
  }
  // {
  //   value: 'week',
  //   label: '按周'
  // },
  // {
  //   value: 'month',
  //   label: '按月'
  // }
]

const publishfrequencyPeriodTOptions = [
  {
    value: '7',
    label: '近7天'
  },
  {
    value: '14',
    label: '近14天'
  }
]
const PublishFrequency = () => {
  const { atUniqueId } = accountDetailStore
  const [frequencyData, setFrequencyData] = useState<any>(null)
  const [frequencyLoading, setFrequencyLoading] = useState(true)
  const [frequencyTypeValue, setFrequencyTypeValue] = useState('day')
  const [dayPeriod, setDayPeriod] = useState('7')
  const [weekPeriod, setWeekPeriod] = useState(() => dayjs().subtract(0, 'month'))
  const [monthPeriod, setMonthPeriod] = useState(() => dayjs().subtract(0, 'year'))

  const renderTypePeriod = () => {
    switch (frequencyTypeValue) {
      case 'day':
        return (
          <Select value={dayPeriod} onChange={setDayPeriod} style={{ width: 100 }}>
            {publishfrequencyPeriodTOptions.map(opt => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        )
      case 'week':
        return (
          <DatePicker
            defaultValue={weekPeriod}
            onChange={setWeekPeriod}
            picker="month"
            allowClear={false}
          />
        )
      case 'month':
        return (
          <DatePicker
            defaultValue={monthPeriod}
            onChange={setMonthPeriod}
            picker="year"
            allowClear={false}
          />
        )
      default:
        break
    }
  }

  const handleFrequencyTypeChange = (value: string) => {
    setFrequencyTypeValue(value)
  }

  const alertText = useMemo(() => {
    const typeLabel = publishFrequencyTypeOptions.find(
      opt => opt.value === frequencyTypeValue
    )?.label
    const total = frequencyData?.releaseCount.reduce((acc: number, cur: number) => acc + cur, 0)

    const avgWork = Number((total / frequencyData?.dateTime.length).toFixed(1))
    const periodLabel = publishfrequencyPeriodTOptions.find(opt => opt.value === dayPeriod)?.label
    return (
      <>
        {periodLabel} 平均<span>每日</span>发布 <span>{formatNumber(avgWork)}</span> 条作品，共发布
        <span> {total}</span> 条作品
      </>
    )
  }, [frequencyTypeValue, dayPeriod, weekPeriod, monthPeriod, frequencyLoading])

  const getPublishFrequencyData = () => {
    setFrequencyLoading(true)
    getPublishFrequency({
      atUniqueId,
      dateType: dayPeriod
    })
      .then(res => {
        if (res.code === 'success') {
          setFrequencyData(res.result)
        }
      })
      .finally(() => {
        setFrequencyLoading(false)
      })
  }

  useEffect(() => {
    getPublishFrequencyData()
  }, [dayPeriod])

  return (
    <div className={styles.publishFrequency}>
      <div className={styles.frequencyHeader}>
        <div className={styles.frequencyTitle}>
          <Text strong>发布频率</Text>
          <div>
            <Select
              value={frequencyTypeValue}
              onChange={handleFrequencyTypeChange}
              style={{ width: 80, marginRight: '12px' }}
            >
              {publishFrequencyTypeOptions.map(opt => (
                <Option key={opt.value} value={opt.value}>
                  {opt.label}
                </Option>
              ))}
            </Select>
            {renderTypePeriod()}
          </div>
        </div>
        <div className={styles.trendInfo}>{alertText}</div>
      </div>
      <div className={styles.frequencyChart}>
        <EChartPrivider
          chartLoading={frequencyLoading}
          data={frequencyData}
          option={getPublishFrequencyOption({
            dateTime: frequencyData?.dateTime || [],
            releaseCount: frequencyData?.releaseCount || []
          })}
        />
      </div>
    </div>
  )
}

// TOP10作品标签
const TopWorksMetricOptions = [
  { value: '1', label: '点赞数' },
  { value: '2', label: '评论数' },
  { value: '3', label: '转发数' }
]

const TopWorksLabel = () => {
  const { atUniqueId } = accountDetailStore
  const [topWorksData, setTopWorksData] = useState<
    { rank: number; tag: string; percentage: number; count: string }[]
  >([])
  const [topWorksLoading, setTopWorksLoading] = useState(true)
  const [topWorksMetric, setTopWorksMetric] = useState('1')

  const getTopWorksData = () => {
    setTopWorksLoading(true)
    getTopWorksLabel({
      atUniqueId,
      sortType: topWorksMetric
    })
      .then(res => {
        if (res.code === 'success') {
          const data = res.result.data.slice(0, 10)
          const total = data.reduce((acc: number, cur: any) => acc + cur.count, 0)

          setTopWorksData(
            res.result.data.slice(0, 10).map((item: any, index: number) => ({
              rank: index + 1,
              tag: item.tag,
              percentage: item.count > 0 ? Math.floor((item.count / total) * 100) : 0,
              count: formatNumber(item.count)
            }))
          )
        }
      })
      .finally(() => {
        setTopWorksLoading(false)
      })
  }
  useEffect(() => {
    getTopWorksData()
  }, [topWorksMetric])
  return (
    <div className={styles.topWorks}>
      <div className={styles.topWorksHeader}>
        <Text strong>TOP10作品标签</Text>
        <Select value={topWorksMetric} onChange={setTopWorksMetric} style={{ width: 120 }}>
          {TopWorksMetricOptions.map(opt => (
            <Option key={opt.value} value={opt.value}>
              {opt.label}
            </Option>
          ))}
        </Select>
      </div>
      <div className={styles.topWorksList}>
        <Flex className={styles.topWorksListHeader}>
          <div className={styles.tagColumn}>标签</div>
          <div className={styles.percentageColumn}>视频占比</div>
          <div className={styles.countColumn}>
            {TopWorksMetricOptions.find(opt => opt.value === topWorksMetric)?.label}
          </div>
        </Flex>
        <div className={styles.topWorksItemList}>
          {topWorksData.map((item, index) => (
            <div key={index} className={styles.topWorksItem}>
              <Flex align="center" style={{ width: 200 }}>
                <span
                  className={styles.rankNumber}
                  style={{
                    background: item.rank === 1 ? '#ffa940' : '#f0f0f0',
                    color: item.rank === 1 ? '#fff' : '#666'
                  }}
                >
                  {item.rank}
                </span>
                <Paragraph ellipsis={{ rows: 1, tooltip: true }} className={styles.tagName}>
                  {item.tag}
                </Paragraph>
              </Flex>
              <Progress percent={item.percentage} style={{ flex: 1 }} />
              <div className={styles.countInfo}>{item.count}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// 监控数据区域
const MonitorSection = observer(() => {
  const { accountDetailData } = accountDetailStore

  if (!accountDetailData) {
    return null
  }

  const { yesterday, today } = accountDetailData
  // 监控数据
  const monitoringData = [
    {
      title: '今日新增粉丝',
      today: today.addFansCount,
      yesterday: yesterday.addFansCount
    },
    {
      title: '今日新增点赞',
      today: today.addLikeCount,
      yesterday: yesterday.addLikeCount
    },
    {
      title: '今日新增评论',
      today: today.addCommentCount,
      yesterday: yesterday.addCommentCount
    },
    {
      title: '今日新增转发',
      today: today.addShareCount,
      yesterday: yesterday.addShareCount
    },
    {
      title: '今日新增收藏',
      today: today.addCollectCount,
      yesterday: yesterday.addCollectCount
    }
  ]

  return (
    <div className={styles.monitorSection}>
      <div className={styles.sectionHeader}>
        <div className={styles.sectionTitle}>
          <Title className={styles.borderTitle} level={5}>
            监控数据
          </Title>
          <Text type="secondary" style={{ marginLeft: '8px' }}>
            仅统计账号当前监控范围内的数据
          </Text>
        </div>
        <div className={styles.updateInfo}>
          <Text style={{ color: '#999' }}>
            更新时间：{formatTimestamp(accountDetailData.lastSyncTime)}
          </Text>
          <Button
            loading={accountDetailStore.refrefreshLoading}
            type="link"
            onClick={() => accountDetailStore.refreshAccountDetail()}
            size="small"
            style={{ padding: 0, marginLeft: '8px' }}
          >
            更新
          </Button>
        </div>
      </div>
      <div className={styles.statsGrid}>
        {monitoringData.map((item, index) => (
          <div key={index} className={styles.statItem}>
            <div className={styles.statLabel}>{item.title}</div>
            <div className={styles.statValue}>
              {formatNumber(item.today)}
              <span className={styles.statSubValue}>昨日 {formatNumber(item.yesterday)}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
})

const AccountOverview = () => {
  return (
    <div className={styles.overviewContent}>
      {/* 监控数据区域 */}
      <MonitorSection />

      {/* 增长趋势图表 */}
      <IncreaseChartSection />

      {/* 账号特征区域 */}
      <div className={styles.characterSection}>
        <div className={styles.sectionHeader}>
          <Title level={5} className={styles.borderTitle}>
            账号特征
          </Title>
        </div>

        <div className={styles.characterContent}>
          {/* 发布频率 */}
          <PublishFrequency />

          {/* TOP10作品标签 */}
          <TopWorksLabel />
        </div>
      </div>
    </div>
  )
}

export default AccountOverview
