import React from 'react'
import { Tag, Input, Space } from 'antd'
import { PlatFormOptions } from '@/constants'

export interface AccountFilterBarValue {
  platform: string
  search: string
}

interface AccountFilterBarProps {
  value: AccountFilterBarValue
  onChange: (val: AccountFilterBarValue) => void
}

const AccountFilterBar: React.FC<AccountFilterBarProps> = ({ value, onChange }) => {
  return (
    <div style={{ background: '#fff', padding: 16, borderRadius: 8 }}>
      {/* 平台筛选 */}
      <div style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
        <div style={{ marginRight: 8, fontWeight: 500, width: 80 }}>平台：</div>
        <Space size={12} wrap style={{ flex: 1 }}>
          {PlatFormOptions.map(opt => (
            <Tag.CheckableTag
              key={opt.value}
              checked={value.platform === opt.value}
              onChange={() => onChange({ ...value, platform: opt.value })}
              className="selectTag"
            >
              <Space size={12}>
                {opt.icon && opt.icon}
                {opt.label}
              </Space>
            </Tag.CheckableTag>
          ))}
        </Space>
      </div>
      {/* 搜索栏 */}
      <div style={{ maxWidth: 400 }}>
        <Input.Search
          placeholder="请输入账号名称搜索"
          allowClear
          onSearch={e => onChange({ ...value, search: e })}
        />
      </div>
    </div>
  )
}

export default AccountFilterBar
